defmodule Admin.Integrations.RND do
  @moduledoc """
  This module represents a RND Integration.

  Helpers can be found at `Admin.Integrations.RND.Helpers`
  SFTP can be found at `Admin.Integrations.RND.ExternalSFTP`
  Test SFTP is mocked.
  """

  alias Admin.Crm.LeadFile.{StagedContact, BulkPhoneMetadata}
  alias Admin.Integrations.RND.{Helpers, ExternalSFTP}

  # SFTP Env
  def sftp_impl, do: Application.get_env(:admin, :rnd_scrub_sftp, ExternalSFTP)

  # Upload RND File
  def  upload_rnd_file(name, file), do: sftp_impl().upload_rnd_file(name, file)

  # Poll SFTP
  def poll_sftp(list_id), do: sftp_impl().poll_sftp(list_id)

  # Download
  def download(list_id),
    do: sftp_impl().download(list_id)

  # Delete
  def delete(list_id, target \\ "/Results?"), do: sftp_impl().delete(list_id, target)


  def download_and_interpret_rnd_results(list, lead_file_id) do
    with {:ok, rnd_results} <- download(lead_file_id) do
      #Results Struct Eg.
      # {:ok,
      #  [
      #    {"7194333460", "2012-01-01", "no_data"},
      #    {"7194603091", "2012-01-01", "no_data"},
      #    {"7194603091", "2012-01-01", "no_data"}
      #  ]}

      # Get all contacts from contact_staging for this lead file
      contacts =
        StagedContact
        |> Ash.Query.filter(lead_file_id == ^lead_file_id)
        |> Ash.read!()

      rnd_results
      |> Enum.map(fn {phone, _date, result} ->
        case result do
          "yes" ->
            # Find contacts that have this phone number in their metadata
            matching_contacts = find_contacts_with_phone(contacts, phone)

            # Update each matching contact
            matching_contacts
            |> Enum.map(fn contact ->
              update_contact_for_rnd_reassignment(contact, phone)
            end)

          _ ->
            # "no_data", "no" - no action needed
            :no_action
        end
      end)
      |> List.flatten()
      |> Enum.filter(&(&1 != :no_action))
    end
  end

  # Find contacts that have the specified phone number in their phones_metadata
  defp find_contacts_with_phone(contacts, phone) do
    contacts
    |> Enum.filter(fn contact ->
      # Handle both StagedContact structs (atom keys) and DataFrame rows (string keys)
      phones_metadata = case contact do
        %{phones_metadata: metadata} -> metadata  # StagedContact struct
        %{"phones_metadata" => metadata} -> metadata  # DataFrame row
        _ -> nil
      end

      case phones_metadata do
        %{all_phones: all_phones} when is_list(all_phones) ->
          # StagedContact struct format
          Enum.any?(all_phones, fn phone_metadata ->
            phone_metadata.phone_number == phone and phone_metadata.included?
          end)
        %{"all_phones" => all_phones} when is_list(all_phones) ->
          # DataFrame row format
          Enum.any?(all_phones, fn phone_metadata ->
            phone_metadata["phone_number"] == phone and phone_metadata["included?"]
          end)
        _ ->
          false
      end
    end)
  end

  # Update a contact when a phone number has been reassigned (RND "yes" result)
  defp update_contact_for_rnd_reassignment(contact, reassigned_phone) do
    # Handle both StagedContact structs and DataFrame rows
    {phones_metadata, all_phones} = case contact do
      %{phones_metadata: %{all_phones: phones}} ->
        {contact.phones_metadata, phones}
      %{"phones_metadata" => %{"all_phones" => phones}} ->
        {contact["phones_metadata"], phones}
      _ ->
        {nil, []}
    end

    updated_all_phones =
      all_phones
      |> Enum.map(fn phone_metadata ->
        # Handle both struct and map formats for phone metadata
        {phone_number, included?, is_struct?} = case phone_metadata do
          %{phone_number: pn, included?: inc} -> {pn, inc, true}
          %{"phone_number" => pn, "included?" => inc} -> {pn, inc, false}
          _ -> {nil, false, false}
        end

        if phone_number == reassigned_phone and included? do
          # Mark this phone as reassigned and excluded
          if is_struct? do
            phone_metadata
            |> Map.put(:rnd?, true)
            |> Map.put(:included?, false)
            |> Map.put(:exclusion_reason, "Reassigned Phone")
            |> Map.put(:line_type, "Uncallable")
          else
            phone_metadata
            |> Map.put("rnd?", true)
            |> Map.put("included?", false)
            |> Map.put("exclusion_reason", "Reassigned Phone")
            |> Map.put("line_type", "Uncallable")
          end
        else
          phone_metadata
        end
      end)

    # Only update if this is a StagedContact struct (not a DataFrame row)
    case contact do
      %StagedContact{} = staged_contact ->
        # Create new bulk metadata with updated phone list
        new_bulk = %BulkPhoneMetadata{
          all_phones: updated_all_phones,
          found_phone_count: length(updated_all_phones),
          unique_valid_phone_count: Enum.count(updated_all_phones, fn phone ->
            case phone do
              %{included?: inc} -> inc
              %{"included?" => inc} -> inc
              _ -> false
            end
          end)
        }

        # Update individual phone field metadata if the reassigned phone matches specific fields
        phone_field_updates = build_phone_field_updates(staged_contact, reassigned_phone)

        # Combine all updates
        all_updates = Map.put(phone_field_updates, :phones_metadata, new_bulk)

        # Update the contact
        staged_contact
        |> Ash.Changeset.for_update(:update, all_updates)
        |> Ash.update!()

      _ ->
        # For DataFrame rows, we can't update directly - this would need to be handled
        # at the DataFrame level. For now, just return the contact unchanged.
        # In practice, this function should be called with StagedContact structs.
        contact
    end
  end

  # Updates for individual phone fields (homephone_dnc?, homephone_line_type, etc.)
  defp build_phone_field_updates(contact, reassigned_phone) do
    phone_fields = [:homephone, :companyphone, :newphone, :altcompanyphone]

    phone_fields
    |> Enum.reduce(%{}, fn field, updates ->
      # Handle both struct and map formats
      phone_value = case contact do
        %{^field => value} -> value  # Struct format
        %{} -> Map.get(contact, Atom.to_string(field))  # Map format with string keys
        _ -> nil
      end

      if phone_value == reassigned_phone do
        dnc_field = String.to_existing_atom("#{field}_dnc?")
        line_type_field = String.to_existing_atom("#{field}_line_type")

        updates
        |> Map.put(dnc_field, true)
        |> Map.put(line_type_field, "Uncallable")
      else
        updates
      end
    end)
  end

end
