defmodule Admin.Integrations.RND do
  @moduledoc """
  This module represents a RND Integration.

  Helpers can be found at `Admin.Integrations.RND.Helpers`
  SFTP can be found at `Admin.Integrations.RND.ExternalSFTP`
  Test SFTP is mocked.
  """

  # SFTP Env
  def sftp_impl, do: Application.get_env(:admin, :rnd_scrub_sftp, ExternalSFTP)

  # Upload RND File
  def  upload_rnd_file(name, data, target \\ "/Results?"), do: sftp_impl().upload_rnd_file(name, data, target)

  # Poll SFTP ???
  def poll_sftp, do: sftp_impl().poll_sftp()

  # Download
  def download(list_id),
    do: sftp_impl().download(list_id)

  # Delete
  def delete(list_id, target \\ "/Results?"), do: sftp_impl().delete(list_id, target)


  def interpret_rnd_results(list, lead_file_id) do
    with {:ok, rnd_results} <- Admin.Integrations.RND.download(lead_file_id) do
      # {:ok,
      #  [
      #    {"7194333460", "2012-01-01", "no_data"},
      #    {"7194603091", "2012-01-01", "no_data"},
      #    {"7194603091", "2012-01-01", "no_data"}
      #  ]}

      rnd_results
      |> Enum.map(fn {phone, date, result} ->

        case result do
          "yes" -> #:reassigned #
            contacts =
            Helpers.get_rnd_contacts(list)
            |> Enum.map(fn contact ->
              contact
                |> Map.put(dnc_field, metadata?.dnc?)
                |> Map.put(line_type_field, metadata?.line_type) #Uncallable

                # These will be the effective result codes
                |> Map.put(:result_code, scrub.result_code)
                |> Map.put(:result_code_nn, scrub.result_code_nn)
                # Store a copy in the scrub* fields for reporting changed codes.
                |> Map.put(:scrub_result_code, scrub.result_code)
                |> Map.put(:scrub_result_code_nn, scrub.result_code_nn)
                |> Map.put(:line_type, scrub.line_type)
                |> Map.put(:rnd?, scrub.reason == "Reassigned")
                |> Map.put(:included?, false)
                |> Map.put(:exclusion_reason, "Reassigned Phone")

                contact
                |> Ash.Changeset.for_update(:update, %{phones_metadata: new_bulk})
                |> Ash.update!()

            end)
          _ -> :unknown #"no_data", "no"
        end


      end)
    end
  end




end
