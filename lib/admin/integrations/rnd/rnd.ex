defmodule Admin.Integrations.RND do
  @moduledoc """
  This module represents a RND Integration.

  Helpers can be found at `Admin.Integrations.RND.Helpers`
  SFTP can be found at `Admin.Integrations.RND.ExternalSFTP`
  Test SFTP is mocked.
  """
  alias Admin.Crm.LeadFile.BulkPhoneMetadata

  # SFTP Env
  def sftp_impl, do: Application.get_env(:admin, :rnd_scrub_sftp, ExternalSFTP)

  # Upload RND File
  def  upload_rnd_file(name, data, target \\ "/Results?"), do: sftp_impl().upload_rnd_file(name, data, target)

  # Poll SFTP ???
  def poll_sftp, do: sftp_impl().poll_sftp()

  # Download
  def download(list_id),
    do: sftp_impl().download(list_id)

  # Delete
  def delete(list_id, target \\ "/Results?"), do: sftp_impl().delete(list_id, target)


  def interpret_rnd_results(list, lead_file_id) do
    with {:ok, rnd_results} <- Admin.Integrations.RND.download(lead_file_id) do
      # {:ok,
      #  [
      #    {"7194333460", "2012-01-01", "no_data"},
      #    {"7194603091", "2012-01-01", "no_data"},
      #    {"7194603091", "2012-01-01", "no_data"}
      #  ]}

      rnd_results
      |> Enum.map(fn {phone, date, result} ->

        case result do
          "yes" ->
            # Get contacts used in the RND process
            contacts = Helpers.get_rnd_contacts(list)

            contacts
            |> Enum.map(fn contact ->
              # Find phone metadata for this phone number
              phone_metadata =
                contact.phones_metadata.all_phones
                |> Enum.find(&(&1.phone_number == phone and &1.included?))

              if phone_metadata do
                # Update the phone metadata attributes
                updated_phone_metadata =
                  phone_metadata
                  |> Map.put(:rnd?, true)
                  |> Map.put(:included?, false)
                  |> Map.put(:exclusion_reason, "Reassigned Phone")
                  |> Map.put(:line_type, "Uncallable")

                # Update all phones metadata
                updated_all_phones =
                  contact.phones_metadata.all_phones
                  |> Enum.map(fn pm ->
                    if pm.phone_number == phone do
                      updated_phone_metadata
                    else
                      pm
                    end
                  end)

                # Create new bulk metadata
                new_bulk = %BulkPhoneMetadata{
                  all_phones: updated_all_phones,
                  found_phone_count: length(updated_all_phones),
                  unique_valid_phone_count: Enum.count(updated_all_phones, & &1.included?)
                }

                # Update the contact in contact_staging
                contact
                |> Ash.Changeset.for_update(:update, %{phones_metadata: new_bulk})
                |> Ash.update!()
              else
                contact
              end
            end)

          _ -> :unknown #"no_data", "no"
        end


      end)
    end
  end


end
