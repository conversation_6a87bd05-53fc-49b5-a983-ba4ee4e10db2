defmodule Admin.Integrations.RND.Helpers do
  @moduledoc """
  Helper functions for the RND process
  """
  alias Admin.Crm.ContactEngine
  alias Admin.Crm.List

  def build_scrub_file(%List{} = list) do
    "Telephone Number List\n"
    <>
    list
    |> ContactEngine.Helpers.select_rnd_phones()
    |> Elixir.List.flatten()
    |> Enum.group_by(fn {phone, _date}-> phone end)
    |> Enum.map(fn {phone, dates} ->
      oldest =
        dates
        |> Enum.sort_by(fn {_phone, date} -> date end)
        |> Enum.at(0)

      "#{phone},#{oldest},C275359519"
    end)
    |> Enum.join("\n")
  end


def get_rnd_contacts(list) do
  list.df
    |> DataFrame.to_rows()
    |> Enum.filter(fn contact ->
      # Check if this contact has any wireless phones that are included
      case contact.phones_metadata do
        %{"all_phones" => all_phones} when is_list(all_phones) ->
          Enum.any?(all_phones, fn phone_metadata ->
            phone_metadata["included?"] and phone_metadata["line_type"] == "Wireless"
          end)
        _ ->
          false
      end
    end)
end


end
