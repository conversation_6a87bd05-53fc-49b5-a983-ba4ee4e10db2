defmodule Admin.Crm.LeadFile.PhoneMetadata do
  @moduledoc """
  This module represents a phone metadata within a Lead File.

  It is used to track and report on the metadata of a phone number, such as the line type, result code, etc.
  """
  use Ash.Resource,
    domain: Admin.Crm.Domain,
    data_layer: :embedded

  attributes do
    attribute :phone_number, :string do
      description "The cleaned phone number"
      public? true
      allow_nil? true
    end

    attribute :line_type, :string do
      description "The line type of the phone number"
      public? true
      allow_nil? true
    end

    attribute :result_code, :string do
      description "The result code of the phone number"
      public? true
      allow_nil? true
    end

    attribute :result_code_nn, :string do
      description "The result code of the phone number for non EBR numbers"
      public? true
      allow_nil? true
    end

    attribute :scrub_result_code, :string do
      description "The result code from DNC Scrub"
      public? true
      allow_nil? true
    end

    attribute :scrub_result_code_nn, :string do
      description "The result code from DNC Scrub"
      public? true
      allow_nil? true
    end

    attribute :litigator?, :boolean do
      description "Whether or not the phone number is a litigator or associated with one"
      public? true
      default false
    end

    attribute :rnd?, :boolean do
      description "Whether or not the phone number has been re-assigned"
      public? true
      default false
    end

    attribute :valid, :boolean do
      description "Whether or not the phone number is valid"
      public? true
      default false
    end

    attribute :npa, :string do
      description "The NPA (Area Code) of the phone number"
      public? true
      allow_nil? true
    end

    attribute :npa_state, :string do
      description "The state of the NPA (Area Code) of the phone number"
      public? true
      allow_nil? true
    end

    attribute :effective_dnc_state, :string do
      description "The effective state for DNC purposes. This is the most restrictive of the NPA state, and the state provided in the address."
      public? true
      allow_nil? true
    end

    attribute :original_phone_number, :string do
      description "The original phone number before any transformations"
      public? true
      allow_nil? true
    end

    attribute :origin_column_name, :string do
      description "The column name the phone number came from"
      public? true
      allow_nil? true
    end

    attribute :destination_column_name, :string do
      description "The column name the phone number was mapped to"
      public? true
      allow_nil? true
    end

    attribute :origin_column_id, :uuid do
      description "The column id of the mapped column the phone number came from"
      public? true
      allow_nil? true
    end

    attribute :included?, :boolean do
      public? true
      default false
    end

    attribute :dnc?, :boolean do
      public? true
      default false
    end

    attribute :exclusion_reason, :string do
      description "If not included, why?"
      public? true
      allow_nil? true
    end

    attribute :duplicate_of_column_name, :string do
      description "If duplicate, which column was it a duplicate of?"
      public? true
      allow_nil? true
    end

    attribute :duplicate_of_column_id, :uuid do
      description "If duplicate, which column id was it a duplicate of?"
      public? true
      allow_nil? true
    end
  end
end
