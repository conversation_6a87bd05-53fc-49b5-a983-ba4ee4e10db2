defmodule Admin.Crm.LeadFile.StagedContact do
  @moduledoc """
  This resource represents a single contact that has been staged for processing.

  It contains all fields for the official contact record, as well as additional fields
  for tracking the status of the contact, as well maintain phone number compliance.
  """
  use Ash.Resource,
    domain: Admin.Crm.Domain,
    data_layer: AshPostgres.DataLayer,
    extensions: [
      AshAdmin.Resource
    ]

  alias Admin.Crm.LeadFile
  alias Admin.Crm.LeadFile.BulkPhoneMetadata

  postgres do
    table "contact_staging"
    repo Admin.AdminRepo
  end

  attributes do
    uuid_primary_key :id

    # MARK: Helper fields
    attribute :landline_count, :integer do
      public? true
      allow_nil? true
    end

    attribute :wireless_count, :integer do
      public? true
      allow_nil? true
    end

    attribute :landline_loaded?, :boolean do
      public? true
      default false
    end

    attribute :wireless_loaded?, :boolean do
      public? true
      default false
    end

    attribute :phones_metadata, BulkPhoneMetadata do
      public? true
      allow_nil? true
    end

    # MARK: 1: HomePhone
    attribute :homephone_dnc?, :boolean do
      public? true
      default false
    end

    attribute :homephone_line_type, :string do
      public? true
      default "Uncallable"
      allow_nil? true
    end

    attribute :homephone_result_code, :string do
      public? true
      description "The effective result code for this number"
      allow_nil? true
    end

    # MARK: 2: CompanyPhone
    attribute :companyphone_dnc?, :boolean do
      public? true
      default false
    end

    attribute :companyphone_line_type, :string do
      public? true
      default "Uncallable"
      allow_nil? true
    end

    attribute :companyphone_result_code, :string do
      public? true
      description "The effective result code for this number"
      allow_nil? true
    end

    # MARK: 3: NewPhone
    attribute :newphone_dnc?, :boolean do
      public? true
      default false
    end

    attribute :newphone_line_type, :string do
      public? true
      default "Uncallable"
      allow_nil? true
    end

    attribute :newphone_result_code, :string do
      public? true
      description "The effective result code for this number"
      allow_nil? true
    end

    # MARK: 4: AltCompanyPhone
    attribute :altcompanyphone_dnc?, :boolean do
      public? true
      default false
    end

    attribute :altcompanyphone_line_type, :string do
      public? true
      default "Uncallable"
      allow_nil? true
    end

    attribute :altcompanyphone_result_code, :string do
      public? true
      description "The effective result code for this number"
      allow_nil? true
    end

    # MARK: Contact Fields
    attribute :accountno, :string do
      public? true
      allow_nil? true
    end

    attribute :fname, :string do
      public? true
      allow_nil? true
    end

    attribute :minitial, :string do
      public? true
      allow_nil? true
    end

    attribute :lname, :string do
      public? true
      allow_nil? true
    end

    attribute :fullname, :string do
      public? true
      allow_nil? true
    end

    attribute :title, :string do
      public? true
      allow_nil? true
    end

    attribute :homeadd1, :string do
      public? true
      allow_nil? true
    end

    attribute :homeadd2, :string do
      public? true
      allow_nil? true
    end

    attribute :homeapt, :string do
      public? true
      allow_nil? true
    end

    attribute :homecity, :string do
      public? true
      allow_nil? true
    end

    attribute :homestate, :string do
      public? true
      allow_nil? true
    end

    attribute :homepostcode, :string do
      public? true
      allow_nil? true
    end

    attribute :homecountry, :string do
      public? true
      allow_nil? true
    end

    attribute :homephone, :string do
      public? true
      allow_nil? true
    end

    attribute :pobox, :string do
      public? true
      allow_nil? true
    end

    attribute :fax, :string do
      public? true
      allow_nil? true
    end

    attribute :email, :string do
      public? true
      allow_nil? true
    end

    # MARK: Company Fields
    attribute :companyname, :string do
      public? true
      allow_nil? true
    end

    attribute :companyadd1, :string do
      public? true
      allow_nil? true
    end

    attribute :companyadd2, :string do
      public? true
      allow_nil? true
    end

    attribute :companycity, :string do
      public? true
      allow_nil? true
    end

    attribute :companystate, :string do
      public? true
      allow_nil? true
    end

    attribute :companypostcode, :string do
      public? true
      allow_nil? true
    end

    attribute :companyphone, :string do
      public? true
      allow_nil? true
    end

    # MARK: Additional Fields
    attribute :newphone, :string do
      public? true
      allow_nil? true
    end

    attribute :receivername, :string do
      public? true
      allow_nil? true
    end

    attribute :authfname, :string do
      public? true
      allow_nil? true
    end

    attribute :authlname, :string do
      public? true
      allow_nil? true
    end

    attribute :authtitle, :string do
      public? true
      allow_nil? true
    end

    attribute :authquest, :string do
      public? true
      allow_nil? true
    end

    attribute :question1, :string do
      public? true
      allow_nil? true
    end

    attribute :question2, :string do
      public? true
      allow_nil? true
    end

    attribute :question3, :string do
      public? true
      allow_nil? true
    end

    attribute :question4, :string do
      public? true
      allow_nil? true
    end

    attribute :question5, :string do
      public? true
      allow_nil? true
    end

    attribute :question6, :string do
      public? true
      allow_nil? true
    end

    attribute :spoketo, :string do
      public? true
      allow_nil? true
    end

    attribute :question4a, :string do
      public? true
      allow_nil? true
    end

    attribute :question4b, :string do
      public? true
      allow_nil? true
    end

    attribute :newfirst, :string do
      public? true
      allow_nil? true
    end

    attribute :newlast, :string do
      public? true
      allow_nil? true
    end

    attribute :question13a, :string do
      public? true
      allow_nil? true
    end

    attribute :question13b, :string do
      public? true
      allow_nil? true
    end

    attribute :emailannounce, :string do
      public? true
      allow_nil? true
    end

    attribute :emailoffers, :string do
      public? true
      allow_nil? true
    end

    attribute :nameref1, :string do
      public? true
      allow_nil? true
    end

    attribute :nameref2, :string do
      public? true
      allow_nil? true
    end

    attribute :emailref1, :string do
      public? true
      allow_nil? true
    end

    attribute :emailref2, :string do
      public? true
      allow_nil? true
    end

    attribute :altcompanyphone, :string do
      public? true
      allow_nil? true
    end

    attribute :numberofmembers, :string do
      public? true
      allow_nil? true
    end

    # MARK: Misc* Fields
    attribute :misc1, :string do
      public? true
      allow_nil? true
    end

    attribute :misc2, :string do
      public? true
      allow_nil? true
    end

    attribute :misc3, :string do
      public? true
      allow_nil? true
    end

    attribute :misc4, :string do
      public? true
      allow_nil? true
    end

    attribute :misc5, :string do
      public? true
      allow_nil? true
    end

    attribute :misc6, :string do
      public? true
      allow_nil? true
    end

    attribute :misc7, :string do
      public? true
      allow_nil? true
    end

    attribute :misc8, :string do
      public? true
      allow_nil? true
    end

    attribute :misc9, :string do
      public? true
      allow_nil? true
    end

    attribute :misc10, :string do
      public? true
      allow_nil? true
    end

    attribute :misc11, :string do
      public? true
      allow_nil? true
    end

    attribute :misc12, :string do
      public? true
      allow_nil? true
    end

    attribute :misc13, :string do
      public? true
      allow_nil? true
    end

    attribute :misc14, :string do
      public? true
      allow_nil? true
    end

    attribute :misc15, :string do
      public? true
      allow_nil? true
    end

    attribute :misc16, :string do
      public? true
      allow_nil? true
    end

    # MARK: New* Fields
    attribute :new17, :string do
      public? true
      allow_nil? true
    end

    attribute :new18, :string do
      public? true
      allow_nil? true
    end

    attribute :new19, :string do
      public? true
      allow_nil? true
    end

    attribute :new20, :string do
      public? true
      allow_nil? true
    end

    attribute :new21, :string do
      public? true
      allow_nil? true
    end

    attribute :new22, :string do
      public? true
      allow_nil? true
    end

    attribute :new23, :string do
      public? true
      allow_nil? true
    end

    attribute :new24, :string do
      public? true
      allow_nil? true
    end

    attribute :new25, :string do
      public? true
      allow_nil? true
    end

    attribute :new26, :string do
      public? true
      allow_nil? true
    end

    attribute :new27, :string do
      public? true
      allow_nil? true
    end

    attribute :new28, :string do
      public? true
      allow_nil? true
    end

    attribute :new29, :string do
      public? true
      allow_nil? true
    end

    attribute :new30, :string do
      public? true
      allow_nil? true
    end

    attribute :new31, :string do
      public? true
      allow_nil? true
    end

    attribute :new32, :string do
      public? true
      allow_nil? true
    end

    attribute :new33, :string do
      public? true
      allow_nil? true
    end

    attribute :new34, :string do
      public? true
      allow_nil? true
    end

    attribute :new35, :string do
      public? true
      allow_nil? true
    end

    attribute :new36, :string do
      public? true
      allow_nil? true
    end

    attribute :new37, :string do
      public? true
      allow_nil? true
    end

    attribute :new38, :string do
      public? true
      allow_nil? true
    end

    attribute :new39, :string do
      public? true
      allow_nil? true
    end

    attribute :new40, :string do
      public? true
      allow_nil? true
    end

    attribute :new41, :string do
      public? true
      allow_nil? true
    end

    attribute :new42, :string do
      public? true
      allow_nil? true
    end

    attribute :new43, :string do
      public? true
      allow_nil? true
    end

    attribute :new44, :string do
      public? true
      allow_nil? true
    end

    attribute :new45, :string do
      public? true
      allow_nil? true
    end

    attribute :new46, :string do
      public? true
      allow_nil? true
    end

    attribute :new47, :string do
      public? true
      allow_nil? true
    end

    attribute :new48, :string do
      public? true
      allow_nil? true
    end

    attribute :new49, :string do
      public? true
      allow_nil? true
    end

    attribute :new50, :string do
      public? true
      allow_nil? true
    end

    attribute :new51, :string do
      public? true
      allow_nil? true
    end

    attribute :new52, :string do
      public? true
      allow_nil? true
    end

    attribute :new53, :string do
      public? true
      allow_nil? true
    end

    attribute :new54, :string do
      public? true
      allow_nil? true
    end

    attribute :new55, :string do
      public? true
      allow_nil? true
    end

    attribute :new56, :string do
      public? true
      allow_nil? true
    end

    attribute :new57, :string do
      public? true
      allow_nil? true
    end

    attribute :new58, :string do
      public? true
      allow_nil? true
    end

    attribute :new59, :string do
      public? true
      allow_nil? true
    end

    attribute :new60, :string do
      public? true
      allow_nil? true
    end

    attribute :new61, :string do
      public? true
      allow_nil? true
    end

    attribute :new62, :string do
      public? true
      allow_nil? true
    end

    attribute :new63, :string do
      public? true
      allow_nil? true
    end

    attribute :new64, :string do
      public? true
      allow_nil? true
    end

    attribute :new65, :string do
      public? true
      allow_nil? true
    end

    attribute :new66, :string do
      public? true
      allow_nil? true
    end

    attribute :new67, :string do
      public? true
      allow_nil? true
    end

    attribute :new68, :string do
      public? true
      allow_nil? true
    end

    attribute :new69, :string do
      public? true
      allow_nil? true
    end

    attribute :new70, :string do
      public? true
      allow_nil? true
    end

    attribute :new71, :string do
      public? true
      allow_nil? true
    end

    attribute :new72, :string do
      public? true
      allow_nil? true
    end

    attribute :new73, :string do
      public? true
      allow_nil? true
    end

    attribute :new74, :string do
      public? true
      allow_nil? true
    end

    attribute :new75, :string do
      public? true
      allow_nil? true
    end

    attribute :new76, :string do
      public? true
      allow_nil? true
    end

    attribute :new77, :string do
      public? true
      allow_nil? true
    end

    attribute :new78, :string do
      public? true
      allow_nil? true
    end

    attribute :new79, :string do
      public? true
      allow_nil? true
    end

    attribute :new80, :string do
      public? true
      allow_nil? true
    end

    attribute :new81, :string do
      public? true
      allow_nil? true
    end

    attribute :new82, :string do
      public? true
      allow_nil? true
    end

    attribute :new83, :string do
      public? true
      allow_nil? true
    end

    attribute :new84, :string do
      public? true
      allow_nil? true
    end

    attribute :new85, :string do
      public? true
      allow_nil? true
    end

    attribute :new86, :string do
      public? true
      allow_nil? true
    end

    attribute :new87, :string do
      public? true
      allow_nil? true
    end

    attribute :new88, :string do
      public? true
      allow_nil? true
    end

    attribute :new89, :string do
      public? true
      allow_nil? true
    end

    attribute :new90, :string do
      public? true
      allow_nil? true
    end

    attribute :new91, :string do
      public? true
      allow_nil? true
    end

    attribute :new92, :string do
      public? true
      allow_nil? true
    end

    attribute :new93, :string do
      public? true
      allow_nil? true
    end

    attribute :new94, :string do
      public? true
      allow_nil? true
    end

    attribute :new95, :string do
      public? true
      allow_nil? true
    end

    attribute :new96, :string do
      public? true
      allow_nil? true
    end

    attribute :new97, :string do
      public? true
      allow_nil? true
    end

    attribute :new98, :string do
      public? true
      allow_nil? true
    end

    attribute :new99, :string do
      public? true
      allow_nil? true
    end

    attribute :new100, :string do
      public? true
      allow_nil? true
    end

    attribute :new101, :string do
      public? true
      allow_nil? true
    end

    attribute :new102, :string do
      public? true
      allow_nil? true
    end

    attribute :new103, :string do
      public? true
      allow_nil? true
    end

    attribute :new104, :string do
      public? true
      allow_nil? true
    end

    attribute :new105, :string do
      public? true
      allow_nil? true
    end

    attribute :new106, :string do
      public? true
      allow_nil? true
    end

    attribute :new107, :string do
      public? true
      allow_nil? true
    end

    attribute :new108, :string do
      public? true
      allow_nil? true
    end

    attribute :new109, :string do
      public? true
      allow_nil? true
    end

    attribute :new110, :string do
      public? true
      allow_nil? true
    end

    attribute :new111, :string do
      public? true
      allow_nil? true
    end

    attribute :new112, :string do
      public? true
      allow_nil? true
    end

    attribute :new113, :string do
      public? true
      allow_nil? true
    end

    attribute :new114, :string do
      public? true
      allow_nil? true
    end

    attribute :new115, :string do
      public? true
      allow_nil? true
    end

    attribute :new116, :string do
      public? true
      allow_nil? true
    end

    attribute :new117, :string do
      public? true
      allow_nil? true
    end

    attribute :new118, :string do
      public? true
      allow_nil? true
    end

    attribute :new119, :string do
      public? true
      allow_nil? true
    end

    attribute :new120, :string do
      public? true
      allow_nil? true
    end

    attribute :new121, :string do
      public? true
      allow_nil? true
    end

    attribute :new122, :string do
      public? true
      allow_nil? true
    end

    attribute :new123, :string do
      public? true
      allow_nil? true
    end

    attribute :new124, :string do
      public? true
      allow_nil? true
    end

    attribute :new125, :string do
      public? true
      allow_nil? true
    end

    attribute :new126, :string do
      public? true
      allow_nil? true
    end

    attribute :new127, :string do
      public? true
      allow_nil? true
    end

    attribute :new128, :string do
      public? true
      allow_nil? true
    end

    attribute :new129, :string do
      public? true
      allow_nil? true
    end

    attribute :new130, :string do
      public? true
      allow_nil? true
    end

    attribute :new131, :string do
      public? true
      allow_nil? true
    end

    attribute :new132, :string do
      public? true
      allow_nil? true
    end

    attribute :delayship, :string do
      public? true
      allow_nil? true
    end

    attribute :notes, :string do
      public? true
      allow_nil? true
    end

    attribute :sourcecode, :string do
      public? true
      allow_nil? true
    end

    attribute :location, :string do
      public? true
      allow_nil? true
    end

    attribute :accountnumber2, :string do
      public? true
      allow_nil? true
    end

    attribute :sourceid, :string do
      public? true
      allow_nil? true
    end

    # MARK: Compliance Fields
    attribute :smsoptin, :string do
      public? true
      allow_nil? true
    end

    attribute :emailoptin, :string do
      public? true
      allow_nil? true
    end

    attribute :qual_date, :date do
      description "START/RENEWAL date of the EBR in YYYY-MM-DD format"
      public? true
      allow_nil? true
    end

    attribute :expire_date, :date do
      description "END of EBR such as date of last magazine. Not used for Qual Date in YYYY-MM-DD format"
      public? true
      allow_nil? true
    end

    attribute :term, :integer do
      description "Length of EBR in months for expire date only"
      public? true
      allow_nil? true
    end
  end

  # MARK: Relationships
  relationships do
    belongs_to :lead_file, LeadFile do
      public? true
      attribute_type :uuid
      attribute_writable? true
    end
  end

  # MARK: Actions
  actions do
    defaults [:destroy, create: :*, update: :*]

    read :read do
      primary? true
      pagination offset?: true, keyset?: true, required?: false
    end

    read :for_sms do
      argument :lead_file_id, :uuid do
        public? true
      end

      # TODO: Utilize ContactEngine metadata
      # Do not include leads from states FL and OK
      filter expr(
               homestate != "FL" and
                 homestate != "OK" and
                 homestate != "LA" and
                 homestate != "FLORIDA" and
                 homestate != "OKLAHOMA" and
                 homestate != "LOUISIANA" and
                 homestate not in lead_file.loads.sms_state_removals and
                 lead_file_id == ^arg(:lead_file_id) and
                 (homephone_line_type == "wireless" or
                    companyphone_line_type == "wireless" or
                    altcompanyphone_line_type == "wireless")
             )
    end
  end

  # MARK: Helper Functions
  def regular_fields, do: ~w(
    accountno
    accountnumber2

    qual_date
    expire_date
    term

    fname
    minitial
    lname
    title
    fullname
    email

    homeadd1
    homeadd2
    homeapt
    homecity
    homecountry
    homepostcode
    homestate

    homephone
    altcompanyphone
    companyphone
    newphone

    sourcecode
    emailoptin
    smsoptin

    companyadd1
    companyadd2
    companycity
    companyname
    companypostcode
    companystate

    location

    nameref1
    nameref2
    new17
    new18
    new19
    new20
    new21
    new22
    new23
    new24
    new25
    new26
    new27
    new28
    new29
    new30
    new31
    new32
    new33
    new34
    new35
    new36
    new37
    new38
    new39
    new40
    new41
    new42
    new43
    new44
    new45
    new46
    new47
    new48
    new49
    new50
    new51
    new52
    new53
    new54
    new55
    new56
    new57
    new58
    new59
    new60
    new61
    new62
    new63
    new64
    new65
    new66
    new67
    new68
    new69
    new70
    new71
    new72
    new73
    new74
    new75
    new76
    new77
    new78
    new79
    new80
    new81
    new82
    new83
    new84
    new85
    new86
    new87
    new88
    new89
    new90
    new91
    new92
    new93
    new94
    new95
    new96
    new97
    new98
    new99
    new100
    new101
    new102
    new103
    new104
    new105
    new106
    new107
    new108
    new109
    new110
    new111
    new112
    new113
    new114
    new115
    new116
    new117
    new118
    new119
    new120
    new121
    new122
    new123
    new124
    new125
    new126
    new127
    new128
    new129
    new130
    new131
    new132

    misc1
    misc2
    misc3
    misc4
    misc5
    misc6
    misc7
    misc8
    misc9
    misc10
    misc11
    misc12
    misc13
    misc14
    misc15
    misc16
    emailannounce
    emailoffers
    emailref1
    emailref2
    newfirst
    newlast
    notes
    numberofmembers
    pobox
    question1
    question13a
    question13b
    question2
    question3
    question4
    question4a
    question4b
    question5
    question6
    receivername
    sourceid
    spoketo
    authfname
    authlname
    authquest
    authtitle
    delayship
    fax)a
  # end def regular_fields
end
