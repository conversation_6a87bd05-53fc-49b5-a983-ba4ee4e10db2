defmodule Admin.Crm.Setups.Setup do
  @moduledoc """
  This record tracks the state of an import.

  Process:

  1. New: A Setup has been prepped.
  2. Staged: A file has been uploaded, the required fields have been
    populated, and contacts have been loaded to the staging area.
  3. Scrubbing: The file has been uploaded to DNC Scrub, all numbers are being scanned.
  4. Analyzing: The results have been fetched and are being analyzed.
  5. Scrubbed: The scrub results are available. Landline and Wireless counts are displayed. The option to load
    one segment (Landline vs Wireless) and or the other is available, and users can come back later and load the
    previously unloaded segment into appropriate dialers; Wireless Dialer may contain Landline phone numbers, Landline
    dialer may not contain Wireless phone numbers.
  6. Loading: The Calling Policy has been defiend and the leads are being loaded.
  7. Loaded: All leads are loaded and marked in the CRM; At least some leads are loaded into the dialers.
  8. Archived: After a period of time, the staging leads will be archived and a new setup and scrub will be
    required to load additional segments of the list.

  Additionally, records may be `:discarded` to abort an unloaded setup; those leads will be de-staged imediately.

  If errors occurr in the process, setups may have the `:error` status. We can check the logs for details.
  """
  use Ash.Resource,
    domain: Admin.Crm.Domain,
    data_layer: AshPostgres.DataLayer,
    extensions: [
      AshStateMachine,
      AshAdmin.Resource
    ]

  require Ash.Query
  require Logger

  alias Admin.Accounts.User
  alias Admin.Crm.LeadFile
  alias Admin.Crm.Setups.DialPolicy

  code_interface do
    define :search, args: [:search_value]
  end

  state_machine do
    initial_states([:new])
    default_initial_state(:new)

    transitions do
      transition :stage do
        from([:new])
        to(:staged)

        # We now have the file uploaded in s3, and in the stagine table
        # We also know ticket info, desired compaign naming
      end

      transition :start_scrubbing do
        from([:error, :staged])
        to(:scrubbing)

        # We have sent the file to DNC Scrub and are awaiting the results
      end

      transition :stop_scrubbing do
        from([:error, :scrubbing])
        to(:scrubbed)

        # We have a copy of the scrubbed file on S3.
      end

      transition :start_analyzing do
        from([:error, :scrubbed])
        to(:analyzing)

        # We have started the analysis of the DNC scrub result.
      end

      transition :stop_analyzing do
        from([:error, :analyzing])
        to(:analyzed)

        # We have analyzed the file and performed the following:
        # 1. Upserted all DNCScrubResult records.
        # 2. Stored the results of the scrub in a related DNCScrubSummary and parts.
      end

      transition :hold do
        from([:analyzed])
        to(:held)
      end

      transition :resume do
        from([:error, :held, :analyzed, :loaded])
        to(:resuming)

        # We have resumed the loading process.
      end

      transition :start_loading do
        from([:resuming, :error, :analyzed, :loaded])
        to(:loading)

        # We have started the loading process.
      end

      transition :stop_loading do
        from([:error, :loading])
        to(:loaded)

        # At least one segment has been loaded into an
        # eligable dialialer.
        # There may be more that are eligable or not.
      end

      transition :archive do
        from([:loaded])
        to(:archived)

        # The staged leads have been removed.
      end

      transition :discard do
        from(:*)
        to(:discarded)

        # We have discarded staged leads, if any existed.
        # The campaign will be eligable for deletion soon.
      end

      transition :recover do
        from(:*)
        to(:new)
      end
    end
  end

  postgres do
    table "setups"
    repo Admin.AdminRepo
  end

  attributes do
    uuid_primary_key :id

    attribute :name, :string do
      public? true
      description "Will be used for project creation."
    end

    attribute :ticket, :string do
      public? true
      allow_nil? false
      description "The ticket ID from the load. Example: GAD-1234"
    end

    attribute :effort_id, :integer do
      default nil
      allow_nil? true
      description "The effort assocaited with the setup."
    end

    attribute :created_parent_ids, {:array, :integer} do
      default nil
      allow_nil? true
      description "If not nil, no new parent will be created."
    end

    attribute :notes, :string do
      public? true

      description """
      Any custom operations performed on the leads before loading.
      For example: Split out Spec or Buy field.
      """
    end

    attribute :do_rnd?, :boolean do
      description "Should the RND (Phone # Reassignment DB) lookup process be performed?"

      public? true
      default false
    end

    attribute :auto_load?, :boolean do
      public? true

      description "Should we speed-run the setup and automatically load the leads after scrubbing, or hold for review."

      default true
    end

    attribute :dial_policy, DialPolicy do
      public? true
      description "In what way are we intending to dial these leads."
    end

    attribute :job_ids, {:array, :integer} do
      public? true

      description "All Oban jobs that have been enqueued for this setup, some may no longer exist."

      default []
    end

    timestamps()
  end

  relationships do
    belongs_to :created_by, User do
      public? true
      attribute_type :integer
      attribute_writable? true
    end

    has_many :lead_files, LeadFile do
      public? true

      description """
      This resource has s3 urls to access the incoming file,
      the scrubbed file, and contains links to contacts in the staging contact table.
      It also contains a breakdown of the scrub results.
      """
    end
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]

    read :setups_for_effort_id do
      argument :effort_id, :integer
      prepare build(filter: expr(effort_id == ^arg(:effort_id)))
    end

    read :search do
      argument :search_value, :string

      prepare build(
                filter:
                  expr(
                    contains(string_downcase(name), string_downcase(^arg(:search_value))) or
                      contains(string_downcase(ticket), string_downcase(^arg(:search_value)))
                  ),
                limit: 10
              )
    end

    read :page do
      pagination offset?: true, default_limit: 15, countable: :by_default

      prepare build(
                select: [
                  :id,
                  :name,
                  :ticket,
                  :created_by_id,
                  :state,
                  :effort_id,
                  :inserted_at,
                  :updated_at
                ],
                load: [:lead_files],
                sort: [updated_at: :desc],
                load: [:created_by]
              )

      filter expr(state not in [:discarded, :archived])
    end

    update :stage do
      accept [:job_ids]
      require_atomic? false
      change transition_state(:staged)
    end

    update :resume do
      accept [:job_ids]
      require_atomic? false
      change transition_state(:resuming)
    end

    update :start_scrubbing do
      require_atomic? false
      accept []
      change transition_state(:scrubbing)
    end

    update :stop_scrubbing do
      require_atomic? false
      accept []
      change transition_state(:scrubbed)
    end

    update :start_analyzing do
      require_atomic? false
      accept []
      change transition_state(:analyzing)
    end

    update :stop_analyzing do
      require_atomic? false
      accept []
      change transition_state(:analyzed)
    end

    update :hold do
      require_atomic? false
      accept []
      change transition_state(:held)
    end

    update :start_loading do
      require_atomic? false
      accept []
      change transition_state(:loading)
    end

    update :stop_loading do
      require_atomic? false
      accept []
      change transition_state(:loaded)
    end

    update :archive do
      require_atomic? false
      # Archived by a user
      change transition_state(:archived)
    end

    update :discard do
      require_atomic? false
      # Discarded by a human
      change transition_state(:discarded)
    end

    update :recover do
      require_atomic? false
      change transition_state(:new)
      change set_attribute(:created_parent_ids, nil)
      change set_attribute(:job_ids, [])
    end
  end

  @doc """
  Creates a standard NN Dial Policy. b2b? defaults to true (common in our field)
  """
  def nn_dial_policy(b2b? \\ true),
    do: %DialPolicy{
      template: :nn,
      b2b?: b2b?,
      ebr?: false
    }

  @doc """
  Creates a standard RQ Dial Policy. b2b? defaults to true (common in our field)
  """
  def rq_dial_policy(b2b? \\ true),
    do: %DialPolicy{
      template: :rq,
      b2b?: b2b?,
      ebr?: true
    }
end
