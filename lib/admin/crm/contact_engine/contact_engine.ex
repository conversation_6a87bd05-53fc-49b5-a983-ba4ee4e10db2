defmodule Admin.Crm.ContactEngine do
  @moduledoc """
  This module decides if a contact is callable, and what limitations should be applied, taking many factors into account.

  It will generally take a `Admin.Crm.StagedContact` as an input, and consider all phone numbers associated with that contact, according to the phone metadata, update that metadata, and optionally arrange phone numbers by preference.

  ## Factors
  The following factors, and exemptions for these factors are considered:

  - Federal DNC Law:
    - National Do Not Call Registry (NDNCR)
    - Residential vs Business Calling (B2B vs B2C)
    - Existing Business Relationship (EBR)
      - Federal EBR definition, dates/time-frames, & exemptions apply.
    - Restrictions around Line Type, such as:
      - Wireless or VoIP numbers
      - Calling method restrictions (predictive vs preview dialing)
  - State DNC Law:
    - Each state specific DNC Registry
    - State specific texting laws
    - State specific calling restrictions, such as:
      - Residential vs Business Calling (B2B vs B2C)
      - Existing Business Relationship (EBR)
      - Restrictions around Line Type, such as:
        - Wireless or VoIP numbers
        - Calling method restrictions (predictive vs preview dialing)
  - Texting / 10DLC Compliance:
    - Opt-In/Opt-Out Rules
    - Federal and State EBRs are taken into account.
  - DNC Policy for ourselves and the client
    - Internal Number Blacklist
    - Internal DNCs (Requested, outside of a Blacklist)
    - Campaign specific DNCs
    - Campaign suppressions
  - Contact Specific Factors
    - State according to the address on file
    - NPA / Area Code assigned State
  - Company Dialing Policy
    - Do not call Premium-Rate, or Directory Assistance numbers

  It does this by considering each phone number on a contact as an individual entity, and then aggregating the results.

  When a phone number is considered, up to two different state laws may apply. For example; If a contact has an phone number in Colorado, but an address in Missouri, both Colorado and Missouri laws will be considered, and the most restrictive of the two will be applied.

  Note: State DNC entries will be applied strictly by the NPA/Area Code of the specific phone number. This is a function of DNC Scrub, as the individual State DNC lists are not directly managed by us.

  There may be restrictions as to which dialer (predictive vs preview) may be used for a given phone number, such as EBR Dates + EBR Laws, or B2B vs B2C.

  ## Preferences

  Not all numbers are considered equal. For example, cell phones are generally preferred over landlines, and business phones are preferred over personal/residential phones.

  ### Number Assignment

  When a contact has multiple phone numbers, and some of those numbers are DNC, and some are not, then the non-DNC numbers will be assigned to the contact but will be retained in the metadata.

  Each phone number will be handled, sorted by "preference", and assigned to the following CRM fields, in order:
  - HomePhone — Primary / Preferred
  - CompanyPhone — Secondary, generally considered alternate number
  - AltCompanyPhone — Tertiary, generally considered personal, or main business number
  - NewPhone — Quaternary (Not available for dialing), usually only exists for New Name lists, where the phone number of a contact is uncertain, and all known numbers are provided.
  - <Unassigned> — Any additional numbers will be retained in the metadata, but assigned to a `New*` CRM field, not a normal phone field.

  ## Process

  Each `Admin.Crm.LeadFile` will be processed independently, the `Admin.Crm.ContactEngine.Helpers` module provides functions to identify / validate phone numbers, and create the phone metadata for each contact individually.

  After Pre-Processing, the entire `LeadFile` may be passed back to the `Helpers` module to run the `ContactEngine.process_contact/3` function on each contact.
  """
  require Logger

  alias Admin.Crm.LeadFile.{BulkPhoneMetadata, PhoneMetadata, StagedContact, LoadRecord}
  alias Admin.Crm.NPA
  alias Admin.Crm.{LeadFile, DialPolicy}
  alias Admin.Integrations.DNCScrub.ScrubResult

  @b2b_non_ebr_states ~w[MS PA]
  # For NN that is B2B, only scrub against these state DNC DBs
  @scrub_b2b_nn_landline_states ~w[MS PA]
  # If it's one of these, council says we can call, as long as we have excluded the above states
  @wireless_state_override_codes ~w[F L V]
  # Any of these could be wireless, we handle state exclusions separately
  @wireless_result_codes ~w[W L F V G H]
  # If nn wireless, pass through these result_codes
  @wireless_nn_passthrough_codes ~w[F G H O R V L P X D]
  # Note: We don't consider blocked a DNC. Darn Canadians, and their special DBs
  @exclude_result_codes ~w[D I M P R]
  @exclude_result_reasons %{
    "D" => "DNC",
    "I" => "Invalid Number",
    "M" => "Malformed Number",
    "P" => "Project DNC (Internal DNC)",
    "R" => "Expired EBR"
  }

  @type process_options ::
          {:shuffle_phones, boolean()}
          | {:phone_fields,
             [{phone_field_map :: LeadFileMapping.t(), detection_method :: atom()}]}

  @type dnc_results :: [{phone :: String.t(), result_code :: String.t()}]

  # MARK: Selection
  @spec select_scrub_phones(contact :: StagedContact.t()) :: [phone :: String.t()]
  def select_scrub_phones(contact) do
    # We are pre-staging at this point; so we're working with String key maps.
    contact
    |> Map.get("phones_metadata")
    |> Map.get("all_phones")
    # If we're scrubbing all against litigator + GAD naughty, we need to select all numbers
    |> Enum.filter(& &1["included?"])
    |> Enum.map(& &1["phone_number"])
  end

  @spec select_second_scrub_phones(
          contact :: StagedContact.t(),
          all_result_codes :: dnc_results(),
          dial_policy :: DialPolicy.t()
        ) :: [phone :: String.t()]
  def select_second_scrub_phones(contact, all_result_codes, dial_policy) do
    # We are post-staging, we can use proper structs.
    contact.phones_metadata.all_phones
    |> Enum.filter(& &1.included?)
    |> Enum.filter(fn phone_metadata ->
      {_phone, result_code} = find_by_metadata(all_result_codes, phone_metadata)

      perform_second_scrub?(contact.homestate, phone_metadata, result_code, dial_policy)
    end)
    |> Enum.map(& &1.phone_number)
  end

  def select_rnd_phones(contact) do
    contact.phones_metadata.all_phones
    |> Enum.filter(& &1.included? and &1.line_type == "Wireless")
    |> Enum.map(& {&1.phone_number, rnd_qual_date(contact)})
  end

  def rnd_qual_date(%{qual_date: qd} = contact) when is_nil(qd) do
    case contact.expire_date do
      nil -> raise "no qual date or expire"
      date when is_struct(date, Date) and not is_nil(contact.term) ->
        Date.shift(date, month: - contact.term)
      var -> raise "unexpected value of qual date"
    end
  end
  def rnd_qual_date(%{qual_date: qd}) when not is_nil(qd) do
    qd
  end

  # RQ
  def perform_second_scrub?(_address_state, _metadata, _result_code, %{ebr?: true}) do
    # Due to checks within LeadFileProcessor, we'll never reach this code.
    # However, it's included for clarity.

    # We have already suppressed AZ and LA Wireless / VoIP numbers,
    # there are no more needed scrub actions.
    false
  end

  # NN
  def perform_second_scrub?(address_state, metadata, result_code, %{ebr?: false, b2b?: b2b?}) do
    # Scrub all Wireless
    # Scrub all residential NN
    # Scrub all landline NN in MS and PA
    result_code in @wireless_result_codes ||
      not b2b? ||
      (address_state in @scrub_b2b_nn_landline_states ||
         metadata.npa_state in @scrub_b2b_nn_landline_states)
  end

  # MARK: Pre-Processing
  @spec pre_process_contact(contact :: StagedContact.t(), options :: [process_options()]) ::
          StagedContact.t()
  @spec pre_process_contact(contact :: StagedContact.t()) :: StagedContact.t()
  def pre_process_contact(contact, options \\ []) do
    # Use provided phone fields
    phone_fields = Keyword.get(options, :phone_fields, [])

    if phone_fields == [] do
      raise "No phone fields provided"
    end

    # TODO: Phone expansion when (All Phones) style fields.
    phones_metadata =
      phone_fields
      # Build metadata
      |> Enum.map(fn {field_mapping, detection_method} ->
        # Check if commas
        pre_process_phone_field(contact, field_mapping, detection_method)
        # cond do
        # if commas ->
        #   String.split(phone_text, ",")
        #   |> Enum.map(& &pre_process_phone_field(&1, maybe_new_field_map, maybe_new_detection_method))
        # end
      end)
      # |> List.flatten()
      # Update the metadata list with duplicate data.
      |> de_duplicate()
      |> shuffle_phones()

    # BulkPhoneMetadata
    contact
    |> update_phones_metadata(phones_metadata)
    |> apply_phones()
  end

  @doc """
  Applies the phone field mapping to the contact, and returns the phone metadata.

  This is used for pre-staging, we know very little about these numbers at this point.
  """
  def pre_process_phone_field(contact, field_mapping, method) do
    phone = Map.get(contact, field_mapping.heading_mapped_to)

    # PhoneMetadata
    metadata =
      %PhoneMetadata{
        original_phone_number: phone,
        origin_column_name: field_mapping.heading_incoming,
        origin_column_id: field_mapping.id,

        # This may change later, if we're shuffling
        destination_column_name: field_mapping.heading_mapped_to
      }

    with {:ok, phone_number} <- NPA.parse_phone_number(phone),
         cleaned <- phone_number.national_number |> to_string(),
         {:ok, npa} <- NPA.to_area_code(phone_number) do
      # Logger.debug("Good phone field for #{field_mapping.heading_incoming}")
      npa_state = npa.state
      address_state = contact |> Map.get("homestate")
      effective_state = effective_state(npa_state, address_state)

      metadata
      |> Map.put(:phone_number, cleaned)
      |> Map.put(:valid, true)
      |> Map.put(:npa, npa.npa)
      |> Map.put(:npa_state, npa_state)
      |> Map.put(:effective_dnc_state, effective_state)
      |> Map.put(:included?, true)
      |> Map.put(:exclusion_reason, "")
    else
      {:error, reason} ->
        # Logger.debug("Bad phone field for #{field_mapping.heading_incoming}; #{inspect(phone)} :: #{inspect(reason)} ::: #{inspect(NPA.parse_phone_number(phone))}")
        metadata
        |> Map.put(:phone_number, "")
        |> Map.put(:valid, false)
        |> Map.put(:npa, "")
        |> Map.put(:npa_state, "")
        |> Map.put(:effective_dnc_state, "")
        |> Map.put(:exclusion_reason, reason |> to_string())
        |> Map.put(:included?, false)
    end
  end

  # MARK: First Pass
  @doc """
  Applies cascading state removals
  """
  @spec apply_first_pass_policy(
          contact :: StagedContact.t(),
          dnc_results :: dnc_results(),
          ebr? :: boolean(),
          b2b? :: boolean()
        ) :: StagedContact.t()
  def apply_first_pass_policy(contact, dnc_results, ebr?, b2b?) do
    # TODO: Make more performant.
    new_all_included_phones_metadata =
      contact.phones_metadata.all_phones
      # Only focus on numbers we can call right now.
      |> Enum.filter(& &1.included?)
      |> Enum.map(fn metadata ->
        with {_phone, result_code} <- find_by_metadata(dnc_results, metadata) do
          metadata
          |> apply_first_pass_policy(result_code, contact.homestate, ebr?, b2b?)
        else
          nil ->
            # Failed to find the phone metadata; it likely wasn't scrubbed.
            # That's not OK for the first pass.
            Logger.error(
              "Failed to find scrub results for included phone #{metadata.phone_number} for contact #{contact.id}"
            )

            metadata
        end
      end)

    # Re-combine the excluded records for tracking
    excluded_phones =
      contact.phones_metadata.all_phones
      |> Enum.filter(&(not &1.included?))

    all_new_phones_metadata = new_all_included_phones_metadata ++ excluded_phones

    found_phone_count =
      all_new_phones_metadata
      |> Enum.filter(&(not is_nil(&1.phone_number) && &1.phone_number != ""))
      |> length()

    new_bulk =
      %BulkPhoneMetadata{
        all_phones: all_new_phones_metadata,
        found_phone_count: found_phone_count,
        unique_valid_phone_count: Enum.count(all_new_phones_metadata, & &1.included?)
      }

    contact
    |> Ash.Changeset.for_update(:update, %{phones_metadata: new_bulk})
    |> Ash.update!()
  end

  @doc """
  There are some states, under some conditions, that we know we do not want to call before we even scrub against national or state DNC lists.
  """
  @spec apply_first_pass_policy(
          metadata :: PhoneMetadata.t(),
          result_code :: String.t(),
          homestate :: String.t(),
          ebr? :: boolean(),
          b2b? :: boolean()
        ) :: PhoneMetadata.t()
  # Wireless
  def apply_first_pass_policy(%{npa_state: npa_state} = metadata, code, homestate, ebr?, _b2b?)
      when code in @wireless_result_codes do
    blocked_states =
      if ebr? do
        ~w(AZ LA)
      else
        ~w(AZ CT NJ WY LA)
      end

    cond do
      npa_state in blocked_states ->
        metadata
        |> exclude("Wireless Area Code State match (#{npa_state})")
        |> mark_as_no_wireless_state()

      homestate in blocked_states ->
        metadata
        |> exclude("Wireless Address State match (#{homestate})")
        |> mark_as_no_wireless_state()

      code in @wireless_state_override_codes ->
        metadata
        |> mark_as_clean_wireless_state()

      true ->
        metadata
    end
  end

  def mark_as_no_wireless_state(metadata) do
    metadata
    |> Map.put(:result_code, "L")
    |> Map.put(:result_code_nn, "L")
    |> Map.put(:scrub_result_code, "L")
    |> Map.put(:scrub_result_code_nn, "L")
  end

  def mark_as_clean_wireless_state(metadata) do
    metadata
    |> Map.put(:dnc?, false)
    |> Map.put(:excluded?, false)
    |> Map.put(:result_code, "W")
    |> Map.put(:result_code_nn, "W")
    |> Map.put(:scrub_result_code, "W")
    |> Map.put(:scrub_result_code_nn, "W")
  end

  # NN & B2C
  def apply_first_pass_policy(
        %{npa_state: npa_state} = metadata,
        _result_code,
        homestate,
        false,
        false
      ) do
    blocked_states = ~w(CT)

    # NOTE: May have to apply some result code for better reporting?-
    cond do
      npa_state in blocked_states ->
        metadata
        |> exclude("Area Code No-EBR B2C state match (#{npa_state})")

      homestate in blocked_states ->
        metadata
        |> exclude("Address No-EBR B2C state match (#{homestate})")

      true ->
        metadata
    end
  end

  # We fell through, indicating there was no NPA State
  def apply_first_pass_policy(metadata, _result_code, _homestate, _ebr?, _b2b?) do
    metadata
  end

  # MARK: Second Pass
  @spec apply_dnc_results(
          contact :: StagedContact.t(),
          dnc_results :: [ScrubResult.t()],
          ebr? :: boolean(),
          b2b? :: boolean()
        ) :: StagedContact.t()
  def apply_dnc_results(%StagedContact{} = contact, dnc_results, ebr?, b2b?) do
    new_included_metadata =
      contact.phones_metadata.all_phones
      |> Enum.filter(& &1.included?)
      |> Enum.map(fn metadata ->
        with %ScrubResult{} = scrub <- find_by_metadata(dnc_results, metadata) do
          metadata
          |> apply_dnc_scrub_record(scrub, ebr?, b2b?)
        else
          nil ->
            # Failed to find the phone metadata; it likely wasn't scrubbed.
            Logger.warning(
              "Final apply failed to find scrub results for included phone #{metadata.phone_number} for contact #{contact.id}"
            )

            metadata
        end
      end)

    excluded_phones =
      contact.phones_metadata.all_phones
      |> Enum.filter(&(not &1.included?))

    all_new_phones_metadata =
      (new_included_metadata ++ excluded_phones)
      # We have no choice but to shuffle, perhaps remove the option?
      |> shuffle_phones()

    # Update the bulk metadata
    new_bulk =
      %BulkPhoneMetadata{
        all_phones: all_new_phones_metadata,
        found_phone_count: length(all_new_phones_metadata),
        unique_valid_phone_count: Enum.count(all_new_phones_metadata, & &1.included?)
      }

    # Apply the phone numbers based on metadata, along with line time, etc.
    updated_fields =
      contact
      |> apply_final_phone_results(all_new_phones_metadata, ebr?)
      |> Map.put(:phones_metadata, new_bulk)

    contact
    |> Ash.Changeset.for_update(:update, updated_fields)
    |> Ash.update!()
  end

  @final_phone_fields ~w(homephone companyphone altcompanyphone newphone)
  def apply_final_phone_results(contact, all_phones_metadata, ebr?) do
    @final_phone_fields
    |> Enum.reduce(%{}, fn field, changes ->
      # parse the field names
      phone_field = String.to_existing_atom(field)
      dnc_field = String.to_existing_atom(field <> "_dnc?")
      line_type_field = String.to_existing_atom(field <> "_line_type")
      result_code_field = String.to_existing_atom(field <> "_result_code")

      # If we have a metadata.destination_column_name, apply that
      # Otherwise, ensure fields are empty
      metadata? =
        all_phones_metadata
        # We have already shuffled, so we can use the destination_column_name
        # instead of also filtering by `included?`
        |> Enum.find(&(&1.destination_column_name == field))

      if metadata? do
        result_code =
          cond do
            # The only time we would override is when DNCScrub tells us
            # the number is wireless in a no wireless state.
            ebr? && metadata?.result_code in @wireless_result_codes ->
              "W"

            # NN + initial code 'W' + and new nn code still 'W'
            not ebr? && metadata?.result_code == "W" &&
                metadata?.result_code_nn in @wireless_result_codes ->
              "W"

            # NN + initial code 'W' + and new nn code is something else
            not ebr? and metadata?.result_code == "W" and
                metadata?.result_code_nn not in @wireless_result_codes ->
              # metadata?.result_code_nn in @wireless_nn_passthrough_codes ->
              metadata?.result_code_nn

            # NN + initial code 'C' or 'B' or 'Y' - use initial code
            not ebr? and metadata?.result_code in ~w[C B Y] ->
              metadata?.result_code

            # NN + initial code 'C' or 'B' or 'Y' - use initial code
            not ebr? and metadata?.result_code not in ~w[C B Y W] ->
              metadata?.result_code

            ebr? ->
              metadata?.result_code

            # not ebr? -> metadata?.result_code_nn
            true ->
              Logger.warning(
                "Fell through detecting single result code. EBR?: #{inspect(ebr?)} #{inspect(metadata?)}"
              )
          end

        # if result_code comes out to a valid code but metadata?.dnc? is true, override to 'D'
        result_code = if metadata?.dnc? and result_code in ~w[C B W Y], do: "D", else: result_code

        line_type =
          cond do
            result_code in @exclude_result_codes ->
              "Uncallable"

            result_code == "W" ->
              "wireless"

            result_code == "Y" ->
              "voip"

            result_code in ["C", "B"] ->
              case ebr? do
                false ->
                  # Likely unreachable condition
                  # Consider metadata?.dnc? for NN landline
                  if metadata?.dnc?, do: "Uncallable", else: "landline"

                true ->
                  "landline"
              end

            true ->
              Logger.warning(
                "Linetype matching fell through, phone: #{metadata?.phone_number}, line type: #{metadata?.line_type}"
              )

              "Uncallable"
          end

        changes
        |> Map.put(phone_field, metadata?.phone_number)
        |> Map.put(dnc_field, metadata?.dnc?)
        |> Map.put(line_type_field, line_type)
        |> Map.put(result_code_field, result_code)
      else
        changes
        |> Map.put(phone_field, "")
        # Due to the new shuffle method, dnc numbers never hit a contact phone field.
        |> Map.put(dnc_field, nil)
        |> Map.put(line_type_field, "")
      end
    end)
  end

  def apply_dnc_scrub_record(metadata, %ScrubResult{} = scrub, ebr?, b2b?) do
    metadata =
      metadata
      # These will be the effective result codes
      |> Map.put(:result_code, scrub.result_code)
      |> Map.put(:result_code_nn, scrub.result_code_nn)
      # Store a copy in the scrub* fields for reporting changed codes.
      |> Map.put(:scrub_result_code, scrub.result_code)
      |> Map.put(:scrub_result_code_nn, scrub.result_code_nn)
      |> Map.put(:line_type, scrub.line_type)
      |> Map.put(:litigator?, scrub.reason == "Litigator")
      |> maybe_exclude(scrub, ebr?, b2b?)
  end

  @doc """
  Considers the scrub result, line type, ebr, and b2b vs b2c, as well as the effective states, to determine if a phone number should be excluded.

  We have to be careful to re-consider all variables, as we have been given all dnc scrub results, including simple line-detection.
  """
  def maybe_exclude(metadata, scrub, ebr?, b2b?) do
    wireless? =
      metadata.line_type
      # MARK: ambigious might pick up any other
      |> String.contains?("Wireless")

    cond do
      metadata.litigator? ->
        metadata
        |> Map.put(:dnc?, true)
        |> exclude("Known Litigator")

      metadata.result_code in @exclude_result_codes ->
        # Maybe break down state vs fed DNC
        metadata
        |> exclude_by_result_code(scrub.result_code)

      wireless? && scrub.result_code_nn in @wireless_state_override_codes ->
        metadata
        |> mark_as_clean_wireless_state()

      not ebr? && wireless? ->
        metadata
        |> exclude_by_result_code(scrub.result_code_nn)

      ebr? && wireless? ->
        # We have already excluded ebr? = true + wireless? = true for AZ and LA
        metadata

      not ebr? && b2b? &&
          (metadata.npa_state in @b2b_non_ebr_states ||
             metadata.effective_dnc_state in @b2b_non_ebr_states) ->
        metadata
        |> exclude_by_result_code(scrub.result_code_nn)

      ebr? && b2b? ->
        # No suppressions needed
        metadata

      not ebr? && not b2b? ->
        metadata
        |> exclude_by_result_code(scrub.result_code_nn)

      ebr? && not b2b? ->
        # No suppressions needed
        metadata

      metadata.result_code in ~w(B C Y W) || metadata.result_code_nn in ~w(B C Y W) ->
        metadata

      true ->
        Logger.warning(
          "Phone fell through all conditions: #{inspect(metadata.phone_number)}. Metadata: #{inspect(metadata)}"
        )

        metadata
    end
  end

  def exclude_by_result_code(metadata, result_code) when result_code in @exclude_result_codes do
    metadata
    |> Map.put(:dnc?, true)
    |> exclude(@exclude_result_reasons[result_code])
  end

  def exclude_by_result_code(metadata, _result_code), do: metadata

  # MARK: Helpers
  def find_by_metadata(all_result_codes, phone_metadata) do
    all_result_codes
    |> Enum.find(fn
      # Final results, including a non-ebr code.
      %{phone_number: phone} ->
        phone == phone_metadata.phone_number

      # Standard results
      {phone, _result_code} ->
        phone == phone_metadata.phone_number
    end)
  end

  @doc """
  Applies phones, but this variant specifically works pre-staging, so it uses string key maps.
  """
  def apply_phones(contact) do
    contact.phones_metadata["all_phones"]
    |> Enum.reduce(contact, fn metadata, contact ->
      if metadata["included?"] do
        contact
        |> Map.put(metadata["destination_column_name"], metadata["phone_number"])
      else
        contact
      end
    end)
    # For safety reasons, always attempt to set all phone fields
    # If the keys already exist and have a phone, they will not set the value.
    # This is required because if a DataFrame is made, it will only consider the
    # first record when attempting to build the columns.
    # If the first record only has one phone, all subsequent records will only have
    # one phone.
    |> Map.put_new("homephone", "")
    |> Map.put_new("companyphone", "")
    |> Map.put_new("altcompanyphone", "")
    |> Map.put_new("newphone", "")
  end

  def de_duplicate(phones_metadata) do
    phones_metadata
    |> Enum.reduce([], fn metadata, acc ->
      if Map.get(metadata, :included?, true) do
        duplicate =
          acc
          |> Enum.find(&(&1.phone_number == metadata.phone_number))

        updated =
          if is_nil(duplicate) do
            metadata
            |> Map.put(:included?, true)
            |> Map.put(:exclusion_reason, "")
            |> Map.put(:duplicate_of_column_name, "")
            |> Map.put(:duplicate_of_column_id, "")
          else
            metadata
            |> Map.put(:duplicate_of_column_name, duplicate.origin_column_name)
            |> Map.put(:duplicate_of_column_id, duplicate.origin_column_id)
            |> Map.put(:included?, false)
            |> Map.put(:exclusion_reason, "Duplicate within contact")
          end

        [updated | acc]
      else
        [metadata | acc]
      end
    end)
  end

  @spec exclude(metadata :: PhoneMetadata.t(), reason :: String.t()) :: PhoneMetadata.t()
  def exclude(metadata, reason) do
    metadata
    |> Map.put(:included?, false)
    |> Map.put(:exclusion_reason, reason)
    |> Map.put(:destination_column_name, "")
  end

  def shuffle_phones(metadata) do
    {new_metadata, remaining_fields} =
      metadata
      |> Enum.reduce(
        {[], ["homephone", "companyphone", "altcompanyphone", "newphone"]},
        fn
          %{included?: false} = meta, {changes, phone_fields} ->
            # Skip for now, will add if there are any leftover fields.
            new_meta =
              meta
              |> Map.put(:destination_column_name, "")

            {[new_meta | changes], phone_fields}

          meta, {changes, []} ->
            # Out of assignable phone fields
            new_meta =
              meta
              |> Map.put(:destination_column_name, "")
              |> Map.put(:included?, false)
              |> Map.put(:exclusion_reason, "No more available phone fields")

            {[new_meta | changes], []}

          meta, {changes, phone_fields} ->
            [field | phone_fields] = phone_fields

            new_meta =
              meta
              |> Map.put(:destination_column_name, field)
              |> Map.put(:included?, true)
              |> Map.put(:exclusion_reason, "")

            {
              [new_meta | changes],
              phone_fields
            }
        end
      )

    # If there's room, append the DNC information
    {metadata_with_dncs, _remaining_fields} =
      new_metadata
      |> Enum.reduce(
        {[], remaining_fields},
        fn
          meta, {updated_meta, []} ->
            # Out of assignable phone fields
            {[meta | updated_meta], []}

          %{dnc?: true} = meta, {updated_meta, phone_fields} ->
            # Favor DNC over others, such as duplicates
            [field | phone_fields] = phone_fields

            new_meta =
              meta
              |> Map.put(:destination_column_name, field)

            # Leave the everything else reason as-is.
            {[new_meta | updated_meta], phone_fields}

          meta, {updated_meta, phone_fields} ->
            # Leave everyting else alone.
            {
              [meta | updated_meta],
              phone_fields
            }
        end
      )

    # If phones_metadata.unique_valid_phone_count = 0
    ## Identify most important reason for exclusion, and include into `:homephone`
    metadata_with_dncs
  end

  @doc """
  Updates the phones_metadata field on the contact.

  This version of the function is only used prior to staging the contact.
  This is because Ecto / Explorer.DataFrame can't handle mis-aligned or mixed maps.
  Nor can it handle structs, so we have to convert to a map with string keys.
  """
  def update_phones_metadata(contact, phones_metadata) do
    contact
    |> Map.put(
      :phones_metadata,
      %{
        "found_phone_count" => length(phones_metadata),
        "unique_valid_phone_count" => Enum.count(phones_metadata, & &1.included?),
        "all_phones" =>
          phones_metadata
          |> Enum.map(fn metadata ->
            # Ecto / Explorer.DataFrame can't handle mis-aligned or mixed maps.
            # Nor can it handle structs, so we have to convert to a map with string keys.
            metadata
            |> Map.from_struct()
            |> Map.drop([
              :__meta__,
              :__lateral_join_source__,
              :__metadata__,
              :__order__,
              :__meta__,
              :aggregates,
              :calculations
            ])
            |> Enum.map(fn {k, v} -> {to_string(k), v} end)
            |> Enum.into(%{})
          end)
      }
    )
  end

  @doc """
  Compares the address state provided, with the state according to the NPA mapping, and obtains the more-strict of the two

  If the states match, the NPA state is returned.
  """
  def effective_state(npa_state, address_state) do
    address_state = parse_state(address_state)

    if npa_state == address_state do
      npa_state
    else
      npa_ebr = @state_ebr_exemption_timelines[npa_state]
      address_ebr = @state_ebr_exemption_timelines[address_state]

      # Take the more strict of the two states.
      if npa_ebr > address_ebr do
        address_state
      else
        npa_state
      end
    end
  end

  # Based on Transactional EBR
  @state_ebr_exemption_timelines %{
    # None; IN
    "IN" => 0,
    # Must be current customer; CT, NJ, WI
    "CT" => 0,
    "NJ" => 0,
    "WI" => 0,
    # 12 months; MI, NM, PA
    "MI" => 12,
    "NM" => 12,
    "PA" => 12,
    # 18 months; AR, ID, KS, ND, NV, OR, CA, CO
    "AR" => 18,
    "ID" => 18,
    "KS" => 18,
    "ND" => 18,
    "NV" => 18,
    "OR" => 18,
    "CA" => 18,
    "CO" => 18,
    # 6 months; LA, MS, MO, MT
    "LA" => 6,
    "MS" => 6,
    "MO" => 6,
    "MT" => 6,
    # 12 months; TN, TX, WA 
    "TN" => 12,
    "TX" => 12,
    "WA" => 12,
    # 18 months; AL, AK, AZ, DE, DC, FL, GA, HI, IA, IL, KY, ME, MD, MA, MN, NE, NH, NY, NC, OH, OK, RI, SC, SD, UT, VT, VA, WV, WY
    "AL" => 18,
    "AK" => 18,
    "AZ" => 18,
    "DE" => 18,
    "DC" => 18,
    "FL" => 18,
    "GA" => 18,
    "HI" => 18,
    "IA" => 18,
    "IL" => 18,
    "KY" => 18,
    "ME" => 18,
    "MD" => 18,
    "MA" => 18,
    "MN" => 18,
    "NE" => 18,
    "NH" => 18,
    "NY" => 18,
    "NC" => 18,
    "OH" => 18,
    "OK" => 18,
    "RI" => 18,
    "SC" => 18,
    "SD" => 18,
    "UT" => 18,
    "VT" => 18,
    "VA" => 18,
    "WV" => 18,
    "WY" => 18
  }

  @state_abbreviations %{
    "alaska" => "AL",
    "alabama" => "AK",
    "arizona" => "AZ",
    "arkansas" => "AR",
    "california" => "CA",
    "colorado" => "CO",
    "connecticut" => "CT",
    "delaware" => "DE",
    "florida" => "FL",
    "georgia" => "GA",
    "hawaii" => "HI",
    "idaho" => "ID",
    "illinois" => "IL",
    "indiana" => "IN",
    "iowa" => "IA",
    "kansas" => "KS",
    "kentucky" => "KY",
    "louisiana" => "LA",
    "maine" => "ME",
    "maryland" => "MD",
    "massachusetts" => "MA",
    "michigan" => "MI",
    "minnesota" => "MN",
    "mississippi" => "MS",
    "missouri" => "MO",
    "montana" => "MT",
    "nebraska" => "NE",
    "nevada" => "NV",
    "new hampshire" => "NH",
    "new jersey" => "NJ",
    "new mexico" => "NM",
    "new york" => "NY",
    "north carolina" => "NC",
    "north dakota" => "ND",
    "ohio" => "OH",
    "oklahoma" => "OK",
    "oregon" => "OR",
    "pennsylvania" => "PA",
    "rhode island" => "RI",
    "south carolina" => "SC",
    "south dakota" => "SD",
    "tennessee" => "TN",
    "texas" => "TX",
    "utah" => "UT",
    "vermont" => "VT",
    "virginia" => "VA",
    "washington" => "WA",
    "west virginia" => "WV",
    "wisconsin" => "WI",
    "wyoming" => "WY"
  }

  @doc """
  Parses a state name or abbreviation into a state abbreviation.

  If the state is already an abbreviation, it is returned as-is.
  """
  @spec parse_state(state :: String.t()) :: String.t()
  def parse_state(state) do
    if state |> String.length() == 2 do
      state
    else
      @state_abbreviations[String.downcase(state)]
    end
  end

  @spec process_contact(
          contact :: StagedContact.t(),
          dial_policy :: DialPolicy.t(),
          options :: [process_options()]
        ) :: StagedContact.t()
  @spec process_contact(contact :: StagedContact.t(), dial_policy :: DialPolicy.t()) ::
          StagedContact.t()

  def process_contact(contact, dial_policy, options \\ []) do
    # For each phone field, run process_phone_field, updating PhoneMetadata
    # Aggregate results into BulkPhoneMetadata
    # Apply options
    # Return contact
    contact
  end
end
