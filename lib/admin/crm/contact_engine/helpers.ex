defmodule Admin.Crm.ContactEngine.Helpers do
  @moduledoc """
  This module contains helper functions for the `Admin.Crm.ContactEngine` module.

  They focus on `Admin.Crm.LeadFile`s (both in pre-loaded, data-frame, state and after staging), while the `ContactEngine` focuses on a single contact. Essentially this module provides standardized looping functionality, and allows the `ContactEngine` to perform detailed checks.

  See `Admin.Crm.ContactEngine` for more information.
  """
  alias Admin.Crm.{List, LeadFile, DialPolicy}

  alias Admin.Crm.LeadFile.{
    BulkPhoneMetadata,
    LeadFileMapping,
    LoadRecord,
    PhoneMetadata,
    StagedContact
  }

  alias Admin.AdminRepo
  alias Admin.Crm.{ContactEngine}

  alias Admin.Integrations.DNCScrub
  alias Admin.Integrations.DNCScrub.ScrubResult

  alias Explorer.DataFrame

  require Ash.Query
  require Ecto.Query
  require Logger

  ## Bulk processing functions
  # Return metrics rather than the lead file?

  # Calls ContactEngine.pre_process for each row in the DF; which creates phones_metadata, and *_metadata for each phone field, though it is incomplete. (<PERSON>les parsing and NPA lookup, but not DNC / Line Type detection)
  @spec pre_process(list :: List.t()) ::
          LeadFile.t() | {:error, error :: String.t()} | {:error, [errors] :: [String.t()]}
  def pre_process(%List{df: df} = list) do
    mapping = mapping_for(list.file.id)
    phone_fields = phone_fields(mapping)

    options = [
      phone_fields: phone_fields,
      shuffle_phones?: do_shuffle?(list.file.loads),
      mapping: mapping
    ]

    # We need to give some better performance consideration to this.
    rows =
      df
      |> DataFrame.to_rows()
      |> Enum.map(fn contact ->
        contact
        |> ContactEngine.pre_process_contact(options)
      end)

    Logger.debug("Re-assembling DataFrame")

    new_df =
      rows
      |> DataFrame.new()

    %List{list | df: new_df}
  end

  @spec apply_first_pass_policy(lead_file_id :: String.t(), ebr? :: boolean(), b2b? :: boolean()) ::
          :ok
  def apply_first_pass_policy(lead_file_id, ebr?, b2b?) do
    # Collect all staged contacts
    contacts =
      StagedContact
      |> Ash.Query.filter(lead_file_id == ^lead_file_id)
      |> Ash.read!()

    dnc_results =
      Ecto.Query.from(
        d in ScrubResult,
        join: c in StagedContact,
        on:
          c.homephone == d.phone_number or
            c.newphone == d.phone_number or
            c.companyphone == d.phone_number or
            c.altcompanyphone == d.phone_number,
        where: c.lead_file_id == ^lead_file_id,
        select: {d.phone_number, d.result_code}
      )
      |> AdminRepo.all()
      |> Enum.uniq_by(fn {phone, _result_code} -> phone end)

    # Filter according to policy
    filtered_contacts =
      contacts
      |> Enum.map(fn contact ->
        # This function pushes the updates to the staged contact.
        # Update the staged contacts
        ContactEngine.apply_first_pass_policy(contact, dnc_results, ebr?, b2b?)
      end)

    :ok
  end

  def do_shuffle?(load_records) do
    load_records
    |> Enum.filter(&(not &1.loaded?))
    |> Enum.at(0)
    |> Map.get(:shuffle_phones?, false)
  end

  @spec select_scrub_phones(list :: List.t()) :: [contact_phones :: [phone :: String.t()]]
  def select_scrub_phones(list) do
    list.df
    |> DataFrame.to_rows()
    |> Enum.map(fn contact ->
      contact
      |> ContactEngine.select_scrub_phones()
    end)
  end

  @spec select_second_scrub_phones(list :: List.t(), dial_policy :: DialPolicy.t()) :: [
          contact_phones :: [phone :: String.t()]
        ]
  def select_second_scrub_phones(list, dial_policy) do
    # Obtain all contacts
    contacts =
      StagedContact
      |> Ash.Query.filter(lead_file_id == ^list.file.id)
      |> Ash.read!()

    # Obtain all result codes
    all_result_codes =
      Ecto.Query.from(
        d in ScrubResult,
        join: c in StagedContact,
        on:
          c.homephone == d.phone_number or
            c.newphone == d.phone_number or
            c.companyphone == d.phone_number or
            c.altcompanyphone == d.phone_number,
        where: c.lead_file_id == ^list.file.id,
        select: {d.phone_number, d.result_code}
      )
      |> AdminRepo.all()
      |> Enum.uniq_by(fn {phone, _result_code} -> phone end)

    # Ask ContactEngine to select contact phones
    contacts
    |> Enum.map(fn contact ->
      contact
      |> ContactEngine.select_second_scrub_phones(all_result_codes, dial_policy)
    end)
  end

  @spec select_rnd_phones(list :: List.t()) :: [contact_phones :: [phone :: String.t()]]
  def select_rnd_phones(list) do
    # Get contacts from contact_staging instead of DataFrame
    contacts =
      StagedContact
      |> Ash.Query.filter(lead_file_id == ^list.file.id)
      |> Ash.read!()

    contacts
    |> Enum.map(fn contact ->
      contact
      |> ContactEngine.select_rnd_phones()
    end)
    |> dbg()
  end

  def apply_dnc_results(list, dial_policy) do
    # Collect all staged contacts
    contacts =
      StagedContact
      |> Ash.Query.filter(lead_file_id == ^list.file.id)
      |> Ash.read!()

    # Gather all dnc results that match
    # TODO: refactor out?
    dnc_results =
      Ecto.Query.from(
        d in ScrubResult,
        join: c in StagedContact,
        on:
          c.homephone == d.phone_number or
            c.newphone == d.phone_number or
            c.companyphone == d.phone_number or
            c.altcompanyphone == d.phone_number,
        where: c.lead_file_id == ^list.file.id,
        select: d
      )
      |> AdminRepo.all()
      |> Enum.uniq_by(fn d -> d.phone_number end)

    # Update the staged contacts
    filtered_contacts =
      contacts
      |> Enum.map(fn contact ->
        contact
        |> ContactEngine.apply_dnc_results(dnc_results, dial_policy.ebr?, dial_policy.b2b?)
      end)

    # Update source metrics
    lead_file_id = DNCScrub.string_to_uuid(list.file.id)
    DNCScrub.fetch_leadfile_sourcemetrics(:homephone, lead_file_id, not dial_policy.ebr?)

    :ok
  end

  # Handles the Post-DNCScrub records, loaded as `Staged Contacts`, updates all metadata fields, optionally "shuffles" phone numbers forward.
  # See ContactEngine for "Shuffle" logic.
  # @spec process(lead_file :: LeadFile.t(), load_record :: LoadRecord.t(), dial_policy :: DialPolicy.t()) :: {:ok, LeadFile.t()} | {:error, error :: String.t()} | {:error, [errors] :: [String.t()]}

  ## Reporting
  # @spec report(lead_file :: LeadFile.t(), load_record :: LoadRecord.t()) :: {:ok, report :: ContactEngine.Report.t()} | {:error, error :: String.t()}
  # @spec update_report(load_record)

  @spec phone_fields([LeadFileMapping.t()]) :: [
          {field_name :: String.t(), detection_method :: atom()}
        ]
  def phone_fields(mappings) do
    mappings
    |> Enum.map(fn map -> {map, phone_field?(map.heading_incoming, map.heading_mapped_to)} end)
    |> Enum.filter(fn {_, {is_phone, _}} -> is_phone end)
    |> Enum.map(fn {field_map, {_, detection_method}} -> {field_map, detection_method} end)
  end

  defp phone_field?(column_name, mapped_to) do
    cond do
      column_name in ~w(homephone companyphone altcompanyphone newphone) ->
        {true, :known_contact_phone_field}

      mapped_to in ~w(homephone companyphone altcompanyphone newphone) ->
        {true, :mapped_to_known_contact_phone_field}

      is_binary(column_name) ->
        match =
          column_name
          |> String.downcase()
          |> String.contains?("phone")

        if match do
          {true, :incoming_name_match}
        else
          {false, :other_field}
        end

      true ->
        {false, :unknown}
    end
  end

  def mapping_for(lead_file_id) do
    LeadFileMapping
    |> Ash.Query.filter(lead_file_id == ^lead_file_id)
    |> Ash.read!()
  end
end
