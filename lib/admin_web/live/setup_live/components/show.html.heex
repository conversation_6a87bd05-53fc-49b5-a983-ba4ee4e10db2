<div class="">
  <div class="flex justify-between px-1 sm:px-0">
    <div>
      <h3 class="sm:flex sm:flex-row sm:items-center sm:gap-3 text-base font-semibold leading-7 text-gray-900 dark:text-brand-50">
        Setup Overview <.id id={@setup.id} type="setup" />
      </h3>
      <.link
        patch={~p"/crm/setups/#{@setup.id}/#{@process_stage}"}
        title="Go to Setup Process"
        class="flex"
      >
        <p
          :if={@setup.state != :loaded}
          class="flex max-w-2xl text-xs hover:text-sm leading-6 italic text-yellow-500 hover:underline"
        >
          Resume Setup (at current stage)
        </p>
        <p
          :if={@setup.state == :loaded}
          class="flex max-w-2xl text-xs hover:text-sm leading-6 italic text-yellow-500 hover:underline"
        >
          View Detials for Jira Ticket
        </p>
        <svg
          class="h-4 w-4 m-1 hover:h-5 hover:w-5 mt-1 text-yellow-500"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="currentColor"
          class="size-6"
        >
          <path
            fill-rule="evenodd"
            d="M7.5 3.75A1.5 1.5 0 0 0 6 5.25v13.5a1.5 1.5 0 0 0 1.5 1.5h6a1.5 1.5 0 0 0 1.5-1.5V15a.75.75 0 0 1 1.5 0v3.75a3 3 0 0 1-3 3h-6a3 3 0 0 1-3-3V5.25a3 3 0 0 1 3-3h6a3 3 0 0 1 3 3V9A.75.75 0 0 1 15 9V5.25a1.5 1.5 0 0 0-1.5-1.5h-6Zm10.72 4.72a.75.75 0 0 1 1.06 0l3 3a.75.75 0 0 1 0 1.06l-3 3a.75.75 0 1 1-1.06-1.06l1.72-1.72H9a.75.75 0 0 1 0-1.5h10.94l-1.72-1.72a.75.75 0 0 1 0-1.06Z"
            clip-rule="evenodd"
          />
        </svg>
      </.link>
    </div>
    <div class="space-x-4">
      <.link patch={~p"/crm/setups/#{@setup.id}/edit"} title="Edit Setup">
        <.badge class="blue" link? title="Edit Setup">
          <Heroicons.pencil outline class="h-5 w-5" />
        </.badge>
      </.link>
      <.badge
        style="red"
        link?
        phx-click="discard"
        data-confirm={"Remove Setup: '#{@setup.name}'? This is a permanent action."}
        title="Discard Setup"
      >
        <Heroicons.trash outline class="h-5 w-5" />
      </.badge>
      <.badge
        style="yellow"
        link?
        phx-click="archive"
        data-confirm={"Archive Setup: '#{@setup.name}'?  This will render projects associated with setup inactive in CRM and ViciDial."}
        title="Archive Setup"
      >
        <Heroicons.archive_box outline class="h-5 w-5" />
      </.badge>
    </div>
  </div>
  <div class="mt-6 border-t border-gray-100">
    <dl class="divide-y divide-gray-100">
      <div class="px-1 py-1 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
        <dt class="text-sm font-medium leading-6 text-gray-900 dark:text-brand-50">Setup Name</dt>
        <dd class="flex flex-row gap-2 mt-1 text-sm leading-6 text-gray-600 dark:text-brand-100 sm:col-span-2 sm:mt-0">
          {@setup.name}
          <.setup_state state={@setup.state} />
        </dd>
      </div>
      <div class="px-1 py-1 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
        <dt class="text-sm font-medium leading-6 text-gray-900 dark:text-brand-50">Ticket</dt>
        <dd class="mt-1 text-sm leading-6 text-blue-500 dark:text-blue-200 hover:font-bold sm:col-span-2 sm:mt-0">
          <.external_link
            href={Enum.join(["https://gad-inc.atlassian.net/browse/", @setup.ticket])}
            text={@setup.ticket}
            image={~p"/images/jira.png"}
          />
        </dd>
      </div>
      <div class="px-1 py-1 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
        <dt class="text-sm font-medium leading-6 text-gray-900 dark:text-brand-50">
          Date Created
        </dt>
        <dd class="mt-1 text-sm leading-6 text-gray-600 dark:text-brand-100 sm:col-span-2 sm:mt-0">
          {AdminWeb.SetupLive.Index.updated_date(@setup.inserted_at)}
        </dd>
      </div>
      <div class="px-1 py-1 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
        <dt class="text-sm font-medium leading-6 text-gray-900 dark:text-brand-50">
          Last Updated
        </dt>
        <dd class="mt-1 text-sm leading-6 text-gray-600 dark:text-brand-100 sm:col-span-2 sm:mt-0">
          {AdminWeb.SetupLive.Index.updated_date(@setup.updated_at)}
        </dd>
      </div>

      <div class="px-1 py-1 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
        <dt class="text-sm font-medium leading-6 text-gray-900 dark:text-brand-50">
          RND Process?
        </dt>
        <dd class="mt-1 text-sm leading-6 text-gray-600 dark:text-brand-100 sm:col-span-2 sm:mt-0">
          {inspect(@setup.do_rnd?)}
        </dd>
      </div>
      <!--<div class="px-1 py-1 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-gray-900">Salary expectation</dt>
            <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">$120,000</dd>
          </div> --->
      <div class="px-1 py-1 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
        <dt class="text-sm font-medium leading-6 text-gray-900 dark:text-brand-50">Notes</dt>
        <dd class="mt-1 text-sm leading-6 text-gray-600 dark:text-brand-100 sm:col-span-2 sm:mt-0">
          {@setup.notes}
        </dd>
      </div>
      <div class="px-1 py-1 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
        <dt class="text-sm font-medium leading-6 text-gray-900 dark:text-brand-50">Lead Files</dt>
        <dd class="mt-2 text-sm text-gray-900 dark:text-brand-100 sm:col-span-2 sm:mt-0">
          <ul
            role="list"
            class="divide-y divide-gray-100 rounded-md border border-gray-200 dark:divide-gray-300 dark:border-gray-400"
          >
            <%= for lf <-  @setup.lead_files do %>
              <li class="flex items-center justify-between py-4 pl-4 pr-5 text-sm leading-6">
                <div class="flex w-0 flex-1 items-center">
                  <svg
                    class="h-5 w-5 shrink-0 text-gray-400 dark:text-brand-100"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    class="size-6"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"
                    />
                  </svg>
                  <div class="ml-4 flex min-w-0 flex-1 gap-2">
                    <a
                      href={~p"/crm/lead_files/#{lf}"}
                      class="flex flex-row gap-1 items-center truncate font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-30"
                    >
                      {lf.name} <.icon name="hero-arrow-right" />
                    </a>
                    <div class="w-32 flex justify-end">
                      <.status_badge file={lf} state={lf.state} />
                    </div>
                  </div>
                </div>
                <!-- <div class="ml-4 shrink-0">
                    <a href={download_link(lf[:url])} download={lf[:name]}  class="font-medium text-indigo-600 hover:text-indigo-500">Download</a>
                  </div> -->
              </li>
            <% end %>
          </ul>
        </dd>
      </div>
      <div class="px-1 py-1 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
        <dt class="text-sm font-medium leading-6 text-gray-900 dark:text-brand-50">Details</dt>
        <dd class="mt-1 text-sm leading-6 text-gray-700 dark:text-brand-100 sm:col-span-2 sm:mt-0">
          <ul
            role="list"
            class="divide-y divide-gray-100 rounded-md border border-gray-200 dark:divide-gray-300 dark:border-gray-400"
          >
            <%= for h <-  @detail_headings do %>
              <li class="flex items-center justify-between py-4 pl-4 pr-5 text-sm leading-6">
                <button
                  id="detail-toggle"
                  value={h}
                  type="button"
                  class="bg-transparent flex w-0 flex-1 items-center"
                  phx-click={
                    if @show_dets == false, do: "details_by_heading", else: "close_details"
                  }
                >
                  <%= if @show_dets == true and h == @selected_heading do %>
                    <Heroicons.minus outline class="h-5 w-5" />
                  <% else %>
                    <Heroicons.plus outline class="h-5 w-5 hover:w-6 hover:h-6" />
                  <% end %>
                  <div class="ml-4 flex min-w-0 flex-1 gap-2">
                    <span class="truncate font-medium text-gray-600 dark:text-brand-100">
                      {h}
                    </span>
                  </div>
                </button>
                <!-- <div class="ml-4 shrink-0">
                    <a href={download_link(lf[:url])} download={lf[:name]}  class="font-medium text-indigo-600 hover:text-indigo-500">Download</a>
                  </div> -->
              </li>
              <div :if={@selected_heading != ""}>
                <div :if={h == @selected_heading} class="px-12 py-1 bg-slate-50 dark:bg-gray-600">
                  <%= if @selected_heading == "DNCScrub Results" and h == "DNCScrub Results" do %>
                    <div :for={lead_file <- @setup.lead_files}>
                      <label class="block text-sm font-light underline text-gray-700 dark:text-brand-100 mt-4">
                        {lead_file.name}
                      </label>
                      <p class="text-sm">Classic Report</p>
                      <%= for c <- AdminWeb.SetupLive.Review.get_leadfile_dncresults(lead_file.id) do %>
                        <p class="whitespace-nowrap py-0 text-sm font-light text-gray-700 dark:text-brand-100">
                          {String.upcase(c)}
                        </p>
                      <% end %>
                      <p class="text-sm mt-3">
                        Phone Metrics Report
                      </p>
                      <dl class="font-light text-gray-700 dark:text-brand-100">
                        <%= for c <- AdminWeb.SetupLive.Review.get_phone_metrics_report(lead_file.id) do %>
                          <div class="sm:grid sm:grid-cols-2 sm:gap-2">
                            <dt>Source Code</dt>
                            <dd>{c.source_code}</dd>
                          </div>

                          <div class="sm:grid sm:grid-cols-2 sm:gap-2">
                            <dt>Gross Names</dt>
                            <dd>{c.gross_names}</dd>
                          </div>

                          <div class="sm:grid sm:grid-cols-2 sm:gap-2">
                            <dt>Net Names</dt>
                            <dd>{c.net_names}</dd>
                          </div>

                          <div class="sm:grid sm:grid-cols-2 sm:gap-2">
                            <dt>Names Processed</dt>
                            <dd>{c.names_processed}</dd>
                          </div>

                          <div class="sm:grid sm:grid-cols-2 sm:gap-2">
                            <dt>Names Lost</dt>
                            <dd>{c.names_lost_count}</dd>
                          </div>

                          <div class="sm:grid sm:grid-cols-2 sm:gap-2">
                            <dt>Numbers Processed</dt>
                            <dd>{c.numbers_processed}</dd>
                          </div>

                          <div class="sm:grid sm:grid-cols-2 sm:gap-2">
                            <dt>Numbers Lost</dt>
                            <dd>{c.numbers_lost_count}</dd>
                          </div>

                          <div class="sm:grid sm:grid-cols-2 sm:gap-2">
                            <dt>Average Gross Numbers per Name</dt>
                            <dd>{c.average_gross_numbers_per_name}</dd>
                          </div>

                          <div class="sm:grid sm:grid-cols-2 sm:gap-2">
                            <dt>Average Net Numbers per Name</dt>
                            <dd>{c.average_net_numbers_per_name}</dd>
                          </div>

                          <dt>Names Lost (Breakdown)</dt>
                          <dd>
                            <div :for={{reason, count} <- c.names_lost} class="text-sm ml-2">
                              {reason} - {count}
                            </div>
                          </dd>

                          <dt>Numbers Lost (Breakdown)</dt>
                          <dd>
                            <div :for={{reason, count} <- c.numbers_lost} class="text-sm ml-2">
                              {reason} - {count}
                            </div>
                          </dd>
                        <% end %>
                      </dl>
                    </div>
                  <% else %>
                    <%= if @selected_heading == "Sources Breakdown" and h == "Sources Breakdown" do %>
                      <%= for lf <- @details do %>
                        <%= for {lfid, source} <- lf do %>
                          <label class="block text-sm font-light text-gray-700 dark:text-brand-100 mt-4">
                            {lfid}
                          </label>
                          <%= for item <- source do %>
                            <p class="text-sm font-light text-gray-500 dark:text-brand-200">
                              {item[:source]} - Record Scrubbed: {item[:record_scrubbed]} | Landline: {item[
                                :landline_loaded
                              ]} | Wireless: {item[:wireless_loaded]}
                            </p>
                          <% end %>
                        <% end %>
                      <% end %>
                    <% else %>
                      <%= if @selected_heading == "Projects by Segment + Campaigns" and h == "Projects by Segment + Campaigns" do %>
                        {@details}
                      <% else %>
                        <%= if @selected_heading == "Load Summary" and h == "Load Summary" do %>
                          {@details}
                        <% end %>
                      <% end %>
                    <% end %>
                  <% end %>
                </div>
              </div>
            <% end %>
          </ul>
        </dd>
      </div>
      <div class="flex min-w-0 flex-1 p-1 justify-left space-x-3 border-1 border-gray-600 dark:border-gray-400 rounded-xs shadow-xs">
        <div class="">
          <img
            :if={
              !is_nil(@setup.created_by_id) and
                AdminWeb.SetupLive.Index.get_user_avatar(@setup.created_by_id) != "none"
            }
            class="h-10 w-10 rounded-lg"
            src={"#{AdminWeb.SetupLive.Index.get_user_avatar(@setup.created_by_id)}"}
            alt=""
          />
        </div>
        <div class="min-w-0 content-right">
          <p class="truncate text-sm font-medium text-gray-500 dark:text-brand-300 italic">
            Created By
          </p>
          <p class="truncate text-sm font-medium text-gray-900 dark:text-brand-50">
            {AdminWeb.SetupLive.Index.get_user_name(@setup.created_by_id)}
          </p>
        </div>
      </div>
    </dl>
  </div>
</div>
