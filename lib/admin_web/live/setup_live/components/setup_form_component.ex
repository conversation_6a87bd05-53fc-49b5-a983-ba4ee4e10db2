defmodule AdminWeb.SetupLive.SetupFormComponent do
  @moduledoc """
  This module is to perform basic edit operations (mainly create a new setup).
  """
  use AdminWeb, :live_component
  import AdminWeb.SetupLive.Components
  alias Admin.Crm.Domain
  alias Admin.Crm.Setups.Setup

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        {@title}
        <:subtitle>
          Use this form to create a new Setup and tie it to a new or existing Parent.
        </:subtitle>
      </.header>

      <.simple_form
        for={@form}
        id="setup_form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <.input field={@form[:name]} type="text" label="Project Name" />
        <.input field={@form[:ticket]} type="text" label="Ticket ID" placeholder="GAD-1234" />
        <.input
          field={@form[:dial_policy_template]}
          type="select"
          label="Dial Policy"
          options={[
            {"RQ (B2B + EBR)", :rq},
            {"NN (B2B, NO EBR)", :nn},
            {"RQ Residential (B2C + EBR)", :rq_residential},
            {"NN Residential (B2C, NO EBR)", :nn_residential}
          ]}
        />
        <.input
          field={@form[:do_rnd?]}
          type="select"
          label="Run RND Process"
          options={[
            {"Run against Reassigned DB", true},
            {"Do not run against Reassigned DB", false}
          ]}
        />
        <.input
          field={@form[:auto_load?]}
          type="select"
          label="Load / Hold"
          options={[
            {"Load after Scrub", true},
            {"Hold for Review", false}
          ]}
        />
        <.input
          field={@form[:notes]}
          type="textarea"
          label="Additional Notes"
          placeholder="Special Pre-processing | Removals"
        />
        <.input field={@form[:created_by_id]} type="hidden" label="" value={@current_user.id} />

        <:actions>
          <.button phx-disable-with="Saving...">Create Setup</.button>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(assigns, socket) do
    {:ok,
     socket
     |> assign(assigns)
     |> assign_form()}
  end

  @impl true
  def handle_event("validate", %{"setup" => setup_params}, socket) do
    {:noreply, assign(socket, form: AshPhoenix.Form.validate(socket.assigns.form, setup_params))}
  end

  def handle_event("save", %{"setup" => setup_params}, socket) do
    templated_params = parse_params(setup_params)

    case AshPhoenix.Form.submit(socket.assigns.form, params: templated_params) do
      {:ok, setup} ->
        notify_parent({:saved, setup})

        socket =
          socket
          |> put_flash(:info, "Setup #{socket.assigns.form.source.type}d successfully")
          |> push_navigate(to: ~p"/crm/setups/#{setup}/upload")

        {:noreply, socket}

      {:error, form} ->
        {:noreply, assign(socket, form: form)}
    end
  end

  def parse_params(%{"dial_policy_template" => "rq"} = params) do
    params
    |> Map.put("dial_policy", %{
      "ebr?" => "true",
      "b2b?" => "true",
      "template" => :rq
    })
  end

  def parse_params(%{"dial_policy_template" => "nn"} = params) do
    params
    |> Map.put("dial_policy", %{
      "ebr?" => "false",
      "b2b?" => "true",
      "template" => :nn
    })
  end

  def parse_params(%{"dial_policy_template" => "rq_residential"} = params) do
    params
    |> Map.put("dial_policy", %{
      "ebr?" => "true",
      "b2b?" => "false",
      "template" => :rq_residential
    })
  end

  def parse_params(%{"dial_policy_template" => "nn_residential"} = params) do
    params
    |> Map.put("dial_policy", %{
      "ebr?" => "false",
      "b2b?" => "false",
      "template" => :nn_residential
    })
  end

  defp notify_parent(msg), do: send(self(), {__MODULE__, msg})

  defp assign_form(%{assigns: %{setup: setup}} = socket) do
    form =
      if setup do
        AshPhoenix.Form.for_update(setup, :update, domain: Domain, as: "setup")
      else
        AshPhoenix.Form.for_create(Setup, :create,
          domain: Domain,
          as: "setup"
        )
      end

    assign(socket, form: to_form(form))
  end
end
