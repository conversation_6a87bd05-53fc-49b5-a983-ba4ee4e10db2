services:
  dev:
    build:
      context: ..
      dockerfile: ./.devcontainer/dev.Dockerfile
    # Override default command so the code machine stays up without a service. 
    command: /bin/sh -c "while sleep 1000; do :; done"
    user: 1000:1000
    volumes:
      - dev-asdf-data:/home/<USER>/.asdf/
      - ..:/workspace:cached
      - ./aliases.zsh:/home/<USER>/.oh-my-zsh/custom/aliases.zsh
      - ~/.ssh/:/workspace/.ssh
    environment:
      DOTENV_FILE: .env.devcontainer
    ports:
      - "4000:4000"
      - "4369:4369"
  livebook:
    image: ghcr.io/livebook-dev/livebook:0.16.4
    ports:
      - "8080:8080"
      - "8081:8081"
    environment:
      LIVEBOOK_TOKEN_ENABLED: false
      LIVEBOOK_DEFAULT_RUNTIME: "attached:app@dev:gimmeh"
    restart: always
    volumes:
      - ../docs/live_books/:/data
  postgres:
    image: timescale/timescaledb:latest-pg16
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data
      - ./admin_dev:/docker-entrypoint-initdb.d/
  wireless:
    image: mariadb:10.5
    command: --port 3307
    environment:
      MARIADB_ALLOW_EMPTY_ROOT_PASSWORD: true
    ports:
      - "3307:3307"
    volumes:
      - wirelessdata:/var/lib/mysql
      - ./wireless_base/:/docker-entrypoint-initdb.d/
  landline:
    image: mariadb:10.5
    command: --port 3308
    environment:
      MARIADB_ALLOW_EMPTY_ROOT_PASSWORD: true
    ports:
      - "3308:3308"
    volumes:
      - landlinedata:/var/lib/mysql
      # Simply clone Wireless again, for now.
      # FIXME: Use a landline base export
      - ./wireless_base/:/docker-entrypoint-initdb.d/
  crm:
    image: mysql:8.0-oracle
    command: --port 3309
    environment:
      MYSQL_ALLOW_EMPTY_PASSWORD: true
    ports:
      - "3309:3309"
    volumes:
      - crmdata:/var/lib/mysql
      - ./crm_base:/docker-entrypoint-initdb.d/
      - ./crm_mysql.cnf:/etc/mysql/conf.d/crm.cnf
  redis:
    image: redis:6.2.6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redisdata:/data
  rabbitmq:
    image: "rabbitmq:management-alpine"
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitdata:/var/lib/rabbitmq
  mongo:
    image: mongo
    # environment:
    # MONGO_INITDB_ROOT_USERNAME: root
    # MONGO_INITDB_ROOT_PASSWORD: example
    volumes:
      - mongodata:/data/db
    ports:
      - "27017:27017"

volumes:
  dev-asdf-data:
  pgdata:
  wirelessdata:
  landlinedata:
  crmdata:
  mongodata:
  redisdata:
  rabbitdata:
  superset-data:
