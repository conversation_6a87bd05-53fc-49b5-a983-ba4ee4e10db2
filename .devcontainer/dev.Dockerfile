FROM ubuntu:24.04

ENV DEBIAN_FRONTEND=noninteractive
ARG USERNAME=ubuntu
ARG USER_UID=1000
ARG USER_GID=$USER_UID

# Common features
ARG INSTALL_ZSH="true"
ARG UPGRADE_PACKAGES="true"
ARG COMMON_SCRIPT_SOURCE="https://raw.githubusercontent.com/microsoft/vscode-dev-containers/v0.231.5/script-library/common-debian.sh"
ARG COMMON_SCRIPT_SHA="3c3543c8aa1f3adba62ae1a9e204846f8448c4cbfee1bce1edaf2a9fefac85df"

# Install required dependencies
RUN apt-get update && \
  apt-get install -y \
  curl \
  git \
  autoconf \
  build-essential \
  libssl-dev \
  libncurses5-dev \
  inotify-tools \
  postgresql \
  postgresql-contrib \
  sudo \
  zsh \
  wget \
  vim fop iputils-ping

# Install dev tools
RUN apt-get install -y \
  mysql-client postgresql-client postgresql-filedump redis-tools

# Install ASDF
RUN set -ex; \
  OS=$(uname -s | tr '[:upper:]' '[:lower:]'); \
  ARCH=$(uname -m); \
  if [ "$ARCH" = "x86_64" ]; then \
  ARCH="amd64"; \
  elif [ "$ARCH" = "aarch64" ] || [ "$ARCH" = "arm64" ]; then \
  ARCH="arm64"; \
  elif [ "$ARCH" = "i386" ] || [ "$ARCH" = "i686" ]; then \
  ARCH="386"; \
  fi; \
  echo "Detected OS: $OS, Architecture: $ARCH"; \
  curl -fSL "https://github.com/asdf-vm/asdf/releases/download/v0.16.3/asdf-v0.16.3-${OS}-${ARCH}.tar.gz" -o asdf.tar.gz; \
  echo "Downloaded asdf-v0.16.3-${OS}-${ARCH}.tar.gz"; \
  tar zxf asdf.tar.gz -C /usr/bin; \
  rm -f asdf.tar.gz;


# Add user
RUN if ! getent group ${USER_GID}; then groupadd --gid ${USER_GID} ${USERNAME}; else \
  EXISTING_GROUP=$(getent group ${USER_GID} | cut -d: -f1); \
  echo "Group with GID ${USER_GID} already exists: ${EXISTING_GROUP}"; fi \
  && if ! id -u ${USER_UID}; then useradd --uid ${USER_UID} --gid ${USER_GID} -m ${USERNAME} -s /bin/zsh; else \
  EXISTING_USER=$(getent passwd ${USER_UID} | cut -d: -f1); \
  echo "User with UID ${USER_UID} already exists: ${EXISTING_USER}"; fi \
  && echo "${USERNAME} ALL=(ALL) NOPASSWD:ALL" > /etc/sudoers.d/${USERNAME} \
  && chmod 0440 /etc/sudoers.d/${USERNAME}

# Copy language install script and make it executable
COPY .devcontainer/scripts/install-languages.sh /usr/bin/install-languages.sh
RUN chmod +x /usr/bin/install-languages.sh

RUN mkdir -p /home/<USER>/.asdf
RUN chown ubuntu:ubuntu /home/<USER>/.asdf
VOLUME /home/<USER>/.asdf

# Install languages and tools for defaults
COPY .tool-versions /home/<USER>/.tool-versions
RUN chown ubuntu:ubuntu /home/<USER>/.tool-versions

ENV PATH="/workspace/.bin:/home/<USER>/.asdf/bin:/home/<USER>/.asdf/shims:$PATH"

USER ${USERNAME}
WORKDIR /home/<USER>

# Install OhMyZSH
# Uses "Spaceship" theme with some customization. Uses some bundled plugins and installs some more from github
RUN sh -c "$(wget -O- https://github.com/deluan/zsh-in-docker/releases/download/v1.2.0/zsh-in-docker.sh)" -- \
  -t https://github.com/denysdovhan/spaceship-prompt \
  -a 'SPACESHIP_PROMPT_ADD_NEWLINE="false"' \
  -a 'SPACESHIP_PROMPT_SEPARATE_LINE="false"' \
  -p git \
  -p ssh-agent \
  -p https://github.com/zsh-users/zsh-autosuggestions \
  -p https://github.com/zsh-users/zsh-completions

# MongoDB
RUN wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add - && \
  echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list && \
  sudo apt update && sudo apt install -y mongodb-mongosh



# Help git not panic
RUN git config --global --add safe.directory /workspace

CMD ["zsh"]
