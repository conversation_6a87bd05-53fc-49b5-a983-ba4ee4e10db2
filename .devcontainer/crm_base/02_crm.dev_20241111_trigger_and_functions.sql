use crm;
drop trigger updated_at;
create trigger updated_at before update on project for each row set new.updated_at = curtime();

DELIMITER $$

DROP PROCEDURE IF EXISTS InsertDial;
CREATE PROCEDURE InsertDial (IN WorkProjectId int) BEGIN
    INSERT INTO dial(ProjectId,SourceId,DialID,PhoneNum)
    SELECT WorkProjectId,c.SourceCode,c.ContactId,IFNULL(c.HomePhone,'')
    FROM contact C
    WHERE ProjectId = WorkProjectId;

    UPDATE contact SET DialId = ContactId
    WHERE ProjectId = WorkProjectId;
END$$

DROP PROCEDURE IF EXISTS GenerateList;
CREATE PROCEDURE GenerateList (IN WorkProjectId Int, IN WorkListId Int) BEGIN
    SELECT
        c.DialId AS 'Vendor Lead Code'
         , c.SourceCode AS 'Source Code'
         , WorkListId AS 'List ID'
         , '1' AS 'Phone Code'
         , c.HomePhone AS 'Phone Number'
         , c.Title AS 'Title'
         , c.Fname AS 'First Name'
         , c.MInitial AS 'Middle Initial'
         , c.LName AS 'Last Name'
         , c.HomeAdd1 AS 'Address Line 1'
         , c.HomeAdd2 AS 'Address Line 2'
         , NULL AS 'Address Line 3'
         , c.HomeCity AS 'City'
         , c.HomeState AS 'State'
         , NULL AS 'Province'
         , c.HomePostCode AS 'Postal Code'
         , c.HomeCountry AS 'Country'
         , NULL AS 'Gender'
         , NULL AS 'Date of Birth'
         , NULL AS 'Alternate Phone Number'
         , c.Email AS 'Email Address'
         , NULL AS 'Security Phrase'
         , c.Notes AS 'Comments'
         , NULL AS 'Rank'
         , NULL AS 'Owner'
    FROM dial D
             JOIN contact C on C.Contactid = d.DialId
    WHERE c.ProjectId = WorkProjectId;
END$$

DROP PROCEDURE IF EXISTS GroupGetPenetratedGroupDialIds;
CREATE PROCEDURE GroupGetPenetratedGroupDialIds(IN WorkGroupProjectId INT, IN WorkContainerProjectId INT) BEGIN
    DECLARE WorkCurDate VARCHAR(25);
    SET WorkCurDate = DATE_FORMAT(NOW(), '%Y%d%m');

    drop temporary table if exists temp_groupreset;
    create temporary table temp_groupreset
    SELECT field1 AS 'Eligable'
    FROM dial d
             LEFT JOIN crc ON crc.crc = d.crc
    WHERE projectid = WorkContainerProjectId -- container
    GROUP BY Field1
    HAVING sum(CASE WHEN IFNULL(crc.finalcrc, 0) = 0
                        THEN 1
                    ELSE 0 END) = 0;

    UPDATE  dial d
    SET crc = 'DNCVA', field4 = CONCAT('FPEN ', WorkCurDate)
    WHERE field1 IN (select Eligable from temp_groupreset)
      AND ProjectId = WorkGroupProjectId
      and crc != 'DNCVA';

    select DialId from dial where ProjectID = WorkGroupProjectId and field4 = CONCAT('FPEN ',WorkCurDate);
END$$

DROP PROCEDURE IF EXISTS CreateProject;
CREATE PROCEDURE CreateProject(IN WorkProjectName VARCHAR(35), IN WorkProjectDescription VARCHAR(255), IN WorkDialer VARCHAR(255)) BEGIN
    INSERT INTO project(ProjectName, Description, Active, Dialer, MaxAttempts)
    VALUES (WorkProjectName, WorkprojectDescription, 1, WorkDialer, 25);
END$$

DROP PROCEDURE IF EXISTS ATKREPORT;
CREATE PROCEDURE ATKREPORT (IN workstart varchar(10), IN workend varchar(10), IN workprojectid int) BEGIN
    DECLARE workDEBUG INT;

    SET workDEBUG = 0;

    IF (workDEBUG = 1) THEN
        #DECLARE workstart varchar(10)
        #DECLARE workend varchar(10)
        #DECLARE workprojectid int

        SET workstart = '04/11/2016';
        SET workend = '04/12/2016';
        SET workprojectid = 6402;
    END IF;
    # get callable names
    DROP TABLE IF EXISTS cc;
    CREATE TEMPORARY TABLE IF NOT EXISTS cc AS (
        select count(dialid)as callable,d.sourceid AS sourcegroup
        from dial d
                 join project p on p.projectid=d.projectid
        WHERE d.DialID > 0
          AND (d.CreateDateTime = '0000-00-00 00:00:00' OR d.CreateDateTime between '2010-01-01' AND workend)
          AND(P.ParentID=workprojectid OR P.ProjectID=workprojectid)
        group by d.sourceid
    );

    # DNCs and Exclusions
    DROP TABLE IF EXISTS ex;
    CREATE TEMPORARY TABLE IF NOT EXISTS ex AS (
        select sum(case crc when 'DNC' then 1 else 0 end) AS 'DNC',sum(case crc when 'DNCX' then 1 else 0 end) AS 'exclusions',d.sourceid AS sourcegroup
        from dial d
                 join project p on p.projectid=d.projectid
        where crc in ('DNC', 'DNCX')
          AND (d.CreateDateTime = '0000-00-00 00:00:00' OR d.CreateDateTime between '2010-01-01' AND workend)
          AND(P.ParentID=workprojectid OR P.ProjectID=workprojectid)
        group by d.sourceid
    );

    # current sales data
    DROP TABLE IF EXISTS cur;
    CREATE TEMPORARY TABLE IF NOT EXISTS cur AS (
        select count(creditcard)-count(rushdelivery) AS CreditCard,count(*)-count(creditcard) AS billme,count(rushdelivery) AS RushDelivery,c.sourcecode AS sourceid
        from (
                 select contactid
                      ,Max(CASE questionid WHEN 318 THEN Response END) AS 'CreditCard'
                      ,Max(CASE WHEN questionid = 23 AND Response = 'RUSH DELIVERY' THEN Response END) AS 'RushDelivery'
                 from responses r join project p on p.projectid=r.projectid
                 where  (P.ParentID=workprojectid OR P.ProjectID=workprojectid)
                 group by contactid
             ) r
                 join contact c on c.contactid=r.contactid
                 join dial d on d.dialid=c.dialid
                 join project p on d.projectid=p.projectid
        where d.crc in ('VS','VSP')
          AND d.lastcalled between workstart AND workend
          and (P.ParentID=workprojectid OR P.ProjectID=workprojectid)
        group by c.sourcecode
    );

    # Cumulative sales data
    DROP TABLE IF EXISTS cum;
    CREATE TEMPORARY TABLE IF NOT EXISTS cum AS (
        select count(creditcard)-count(rushdelivery) AS CreditCard,count(*)-count(creditcard) AS billme,count(rushdelivery) AS RushDelivery,c.sourcecode AS sourceid
        from (
                 select contactid
                      ,Max(CASE questionid WHEN 318 THEN Response END) AS 'CreditCard'
                      ,Max(CASE WHEN questionid = 23 AND Response = 'RUSH DELIVERY' THEN Response END) AS 'RushDelivery'
                 from responses r join project p on p.projectid=r.projectid
                 where  (P.ParentID=workprojectid OR P.ProjectID=workprojectid)
                 group by contactid
             ) r
                 join contact c on c.contactid=r.contactid
                 join dial d on d.dialid=c.dialid
                 join project p on d.projectid=p.projectid
        where d.crc in ('VS','VSP')
          AND d.lastcalled between '2010-01-01' AND workend
          and (P.ParentID=workprojectid OR P.ProjectID=workprojectid)
        group by c.sourcecode
    );

    # leads used within date range
    DROP TABLE IF EXISTS ctlu;
    CREATE TEMPORARY TABLE IF NOT EXISTS ctlu AS (
        select count(*) AS numberof, sourceid
        from dial h
                 join crc on crc.crc=h.crc
                 join project p on p.projectid=h.projectid
        where crc.finalcrc=1
          AND h.lastcalled between workstart AND workend
          and (P.ParentID=workprojectid OR P.ProjectID=workprojectid )
        group by sourceid
    );

    # Leads used throughout lifetime of project
    DROP TABLE IF EXISTS ttlu;
    CREATE TEMPORARY TABLE IF NOT EXISTS ttlu AS (
        select count(*) AS numberof, sourceid
        from dial h
                 join crc on crc.crc=h.crc
                 join project p on p.projectid=h.projectid
        where crc.finalcrc=1
          AND h.lastcalled between '2010-01-01' AND workend
          and (P.ParentID=workprojectid OR P.ProjectID=workprojectid )
        group by sourceid
    );

    select D.sourceid AS sourcegroup
         ,sum(grossnames) AS grossnames
         ,d.description
         ,sum(callable) AS callable
         ,sum(exclusions) AS exclusions
         ,sum(cur.BillMe) AS BillMe
         ,sum(cum.BillMe) AS TBillMe
         ,sum(cur.CreditCard) AS CreditCard
         ,sum(cum.CreditCard) AS TCreditCard
         ,sum(cur.RushDelivery) AS RushDelivery
         ,sum(cum.RushDelivery) AS TRushDelivery
         ,sum(ctlu.numberof) AS ctlu
         ,sum(ttlu.numberof) AS ttlu
         ,sum(DNC) AS DNC
    FROM grossnames D
             join project p on d.projectid=p.projectid
             join cc cc on cc.sourcegroup=D.sourceid
             left JOIN ex on ex.sourcegroup=d.sourceid
             left JOIN cur on cur.sourceid=D.sourceid
             left JOIN cum on cum.sourceid=D.sourceid
             left join ctlu on ctlu.sourceid=D.sourceid
             left join ttlu on ttlu.sourceid=D.sourceid
    WHERE  (P.ParentID=workprojectid  OR P.ProjectID=workprojectid)
    #	and d.sourceid in (	select distinct sourceid from dial h join project p on p.projectid=h.projectid where (p.projectid=workprojectid or p.parentid=workprojectid) /*and lastcalled between workstart AND workend*/ )
    group by D.sourceid,d.description
    order by D.sourceid;
END$$

DROP PROCEDURE IF EXISTS ProcessGroupCalling;
CREATE PROCEDURE ProcessGroupCalling(IN WorkSinglesProjectId INT, IN WorkContainerProjectId INT, IN WorkGroupProjectId INT)
BEGIN
    # Notes:
    # Field1 = GroupID
    # Field2 = ContainerProjectID
    # G.C.New98 = Count
    # G.C.New99 = Field1

    DROP TEMPORARY TABLE IF EXISTS GroupProcessingTemp;
    CREATE TEMPORARY TABLE GroupProcessingTemp
    (
        Field1      INT PRIMARY KEY AUTO_INCREMENT,
        HomePhone    VARCHAR(20),
        Count       INT,
        FirstSource VARCHAR(32)
    );

    # Find group contacts
    INSERT INTO GroupProcessingTemp(HomePhone, Count, FirstSource)
    SELECT
        HomePhone,
        COUNT(HomePhone) AS 'Count',
        MIN(SourceCode) AS FirstSource
    FROM contact C
    WHERE ProjectID = WorkSinglesProjectId #Singles
    GROUP BY HomePhone
    HAVING COUNT(HomePhone) > 1;


    # Insert Group Contacts
    INSERT INTO contact (ProjectID, FName, LName, HomePhone, SourceCode, New98, New99)
    SELECT
        WorkGroupProjectId AS ProjectID,
        'Grouped Record' AS 'FName',
        'Grouped Record' AS 'LName',
        HomePhone,
        FirstSource AS SourceCode,
        Count AS New98,
        Field1 as New99
    FROM GroupProcessingTemp;

    # Move Contacts that matched Gs
    UPDATE contact C
        JOIN GroupProcessingTemp GPT on C.HomePhone = GPT.HomePhone
    SET C.ProjectID = WorkContainerProjectId #Container
    WHERE C.ProjectID = WorkSinglesProjectId; #Singles

    # Insert Singles
    INSERT INTO dial (ProjectID, SourceID, DialID, PhoneNum)
    SELECT WorkSinglesProjectId, C.SourceCode, c.ContactID, IFNULL(C.HomePhone,'')
    FROM contact C WHERE C.ProjectID = WorkSinglesProjectId; #Singles

    # Insert Groups
    INSERT INTO dial (ProjectID, SourceID, DialID, PhoneNum, Field1, Field2)
    SELECT WorkGroupProjectId, C.SourceCode, c.ContactID, IFNULL(C.HomePhone,''), C.New99, WorkContainerProjectId #Container
    FROM contact C WHERE C.ProjectID = WorkGroupProjectId; #Group

    # Insert Container
    INSERT INTO dial (ProjectID, SourceID, DialID, PhoneNum, Field1)
    SELECT WorkContainerProjectId, C.SourceCode, c.ContactID, IFNULL(C.HomePhone,''), G.Field1
    FROM contact C JOIN GroupProcessingTemp G on C.HomePhone = G.HomePhone WHERE C.ProjectID = WorkContainerProjectId; #Container

    UPDATE contact SET DialID = ContactID WHERE ProjectID IN (WorkSinglesProjectId,WorkGroupProjectId,WorkContainerProjectId);
END$$

DELIMITER ;