insert into asterisk.vicidial_campaigns (campaign_id, campaign_name, active, dial_status_a, dial_status_b, dial_status_c, dial_status_d, dial_status_e, lead_order, park_ext, park_file_name, web_form_address, allow_closers, hopper_level, auto_dial_level, next_agent_call, local_call_time, voicemail_ext, dial_timeout, dial_prefix, campaign_cid, campaign_vdad_exten, campaign_rec_exten, campaign_recording, campaign_rec_filename, campaign_script, get_call_launch, am_message_exten, amd_send_to_vmx, xferconf_a_dtmf, xferconf_a_number, xferconf_b_dtmf, xferconf_b_number, alt_number_dialing, scheduled_callbacks, lead_filter_id, drop_call_seconds, drop_action, safe_harbor_exten, display_dialable_count, wrapup_seconds, wrapup_message, closer_campaigns, use_internal_dnc, allcalls_delay, omit_phone_code, dial_method, available_only_ratio_tally, adaptive_dropped_percentage, adaptive_maximum_level, adaptive_latest_server_time, adaptive_intensity, adaptive_dl_diff_target, concurrent_transfers, auto_alt_dial, auto_alt_dial_statuses, agent_pause_codes_active, campaign_description, campaign_changedate, campaign_stats_refresh, campaign_logindate, dial_statuses, disable_alter_custdata, no_hopper_leads_logins, list_order_mix, campaign_allow_inbound, manual_dial_list_id, default_xfer_group, xfer_groups, queue_priority, drop_inbound_group, qc_enabled, qc_statuses, qc_lists, qc_shift_id, qc_get_record_launch, qc_show_recording, qc_web_form_address, qc_script, survey_first_audio_file, survey_dtmf_digits, survey_ni_digit, survey_opt_in_audio_file, survey_ni_audio_file, survey_method, survey_no_response_action, survey_ni_status, survey_response_digit_map, survey_xfer_exten, survey_camp_record_dir, disable_alter_custphone, display_queue_count, manual_dial_filter, agent_clipboard_copy, agent_extended_alt_dial, use_campaign_dnc, three_way_call_cid, three_way_dial_prefix, web_form_target, vtiger_search_category, vtiger_create_call_record, vtiger_create_lead_record, vtiger_screen_login, cpd_amd_action, agent_allow_group_alias, default_group_alias, vtiger_search_dead, vtiger_status_call, survey_third_digit, survey_third_audio_file, survey_third_status, survey_third_exten, survey_fourth_digit, survey_fourth_audio_file, survey_fourth_status, survey_fourth_exten, drop_lockout_time, quick_transfer_button, prepopulate_transfer_preset, drop_rate_group, view_calls_in_queue, view_calls_in_queue_launch, grab_calls_in_queue, call_requeue_button, pause_after_each_call, no_hopper_dialing, agent_dial_owner_only, agent_display_dialable_leads, web_form_address_two, waitforsilence_options, agent_select_territories, campaign_calldate, crm_popup_login, crm_login_address, timer_action, timer_action_message, timer_action_seconds, start_call_url, dispo_call_url, xferconf_c_number, xferconf_d_number, xferconf_e_number, use_custom_cid, scheduled_callbacks_alert, queuemetrics_callstatus_override, extension_appended_cidname, scheduled_callbacks_count, manual_dial_override, blind_monitor_warning, blind_monitor_message, blind_monitor_filename, inbound_queue_no_dial, timer_action_destination, enable_xfer_presets, hide_xfer_number_to_dial, manual_dial_prefix, customer_3way_hangup_logging, customer_3way_hangup_seconds, customer_3way_hangup_action, ivr_park_call, ivr_park_call_agi, manual_preview_dial, realtime_agent_time_stats, use_auto_hopper, auto_hopper_multi, auto_hopper_level, auto_trim_hopper, api_manual_dial, manual_dial_call_time_check, display_leads_count, lead_order_randomize, lead_order_secondary, per_call_notes, my_callback_option, agent_lead_search, agent_lead_search_method, queuemetrics_phone_environment, auto_pause_precall, auto_pause_precall_code, auto_resume_precall, manual_dial_cid, post_phone_time_diff_alert, custom_3way_button_transfer, available_only_tally_threshold, available_only_tally_threshold_agents, dial_level_threshold, dial_level_threshold_agents, safe_harbor_audio, safe_harbor_menu_id, survey_menu_id, callback_days_limit, dl_diff_target_method, disable_dispo_screen, disable_dispo_status, screen_labels, status_display_fields, na_call_url, survey_recording, pllb_grouping, pllb_grouping_limit, call_count_limit, call_count_target, callback_hours_block, callback_list_calltime, user_group, hopper_vlc_dup_check, in_group_dial, in_group_dial_select, safe_harbor_audio_field, pause_after_next_call, owner_populate, use_other_campaign_dnc, allow_emails, amd_inbound_group, amd_callmenu, survey_wait_sec, manual_dial_lead_id, dead_max, dead_max_dispo, dispo_max, dispo_max_dispo, pause_max, max_inbound_calls, manual_dial_search_checkbox, hide_call_log_info, timer_alt_seconds, wrapup_bypass, wrapup_after_hotkey, callback_active_limit, callback_active_limit_override, allow_chats, comments_all_tabs, comments_dispo_screen, comments_callback_screen, qc_comment_history, show_previous_callback, clear_script, cpd_unknown_action, manual_dial_search_filter, web_form_address_three, manual_dial_override_field, status_display_ingroup, customer_gone_seconds, agent_display_fields, am_message_wildcards, manual_dial_timeout, routing_initiated_recordings, manual_dial_hopper_check, callback_useronly_move_minutes, ofcom_uk_drop_calc, manual_auto_next, manual_auto_show, allow_required_fields, dead_to_dispo, agent_xfer_validation, ready_max_logout, callback_display_days, three_way_record_stop, hangup_xfer_record_start, scheduled_callbacks_email_alert, max_inbound_calls_outcome, manual_auto_next_options, agent_screen_time_display, next_dial_my_callbacks, inbound_no_agents_no_dial_container, inbound_no_agents_no_dial_threshold, cid_group_id, pause_max_dispo, script_top_dispo, dead_trigger_seconds, dead_trigger_action, dead_trigger_repeat, dead_trigger_filename, dead_trigger_url, scheduled_callbacks_force_dial, scheduled_callbacks_auto_reschedule, scheduled_callbacks_timezones_container, three_way_volume_buttons, callback_dnc, manual_dial_validation, mute_recordings, auto_active_list_new, call_quota_lead_ranking, call_quota_process_running, call_quota_last_run_date, sip_event_logging, campaign_script_two, leave_vm_no_dispo, leave_vm_message_group_id, dial_timeout_lead_container, amd_type, vmm_daily_limit, opensips_cid_name, amd_agent_route_options, browser_alert_sound, browser_alert_volume, three_way_record_stop_exception, pause_max_exceptions, hopper_drop_run_trigger, daily_call_count_limit, daily_limit_manual, transfer_button_launch, shared_dial_rank, agent_search_method, qc_scorecard_id, qc_statuses_id, clear_form, leave_3way_start_recording, leave_3way_start_recording_exception, calls_waiting_vl_one, calls_waiting_vl_two, calls_inqueue_count_one, calls_inqueue_count_two, in_man_dial_next_ready_seconds, in_man_dial_next_ready_seconds_override, transfer_no_dispo, call_limit_24hour_method, call_limit_24hour_scope, call_limit_24hour, call_limit_24hour_override, cid_group_id_two, incall_tally_threshold_seconds, auto_alt_threshold, pause_max_url, agent_hide_hangup, ig_xfer_list_sort, script_tab_frame_size, max_logged_in_agents, user_group_script)
values  ('BASE', 'Base Campaign', 'N', '', '', '', '', '', 'RANDOM', '', '', '', 'Y', 100, '1', 'longest_wait_time', '8am-8pm', '', 50, '997', '7207299878', '8368', '8309', 'ALLFORCE', 'FULLDATE_CUSTPHONE', 'NEWSCRSSL', 'SCRIPT', 'SIP-6-silence', 'N', '', '', '', '', 'Y', 'N', 'NONE', 7, 'HANGUP', '8307', 'Y', 0, 'Wrapup Call', null, 'N', 0, 'N', 'RATIO', 'N', '30', '4.0', '1400', '0', 0, 'AUTO', 'NONE', ' B N NA DC -', 'N', '', '2024-05-29 12:15:10', 'N', '2016-10-12 16:02:29', ' NA AL AHK SVYHU PU PDROP NLP N ERI DROP CB AA B UA A UAWR NEW -', 'N', 'Y', 'DISABLED', 'N', 998, '---NONE---', '', 50, '---NONE---', 'Y', ' SA SALE -', '', '24HRMIDNIGHT', 'NONE', 'Y', '', '', 'US_pol_survey_hello', '1238', '8', 'US_pol_survey_transfer', 'US_thanks_no_contact', 'AGENT_XFER', 'OPTIN', 'NI', '1-DEMOCRAT|2-REPUBLICAN|3-INDEPENDANT|8-OPTOUT|X-NO RESPONSE|', '8300', '/home/<USER>', 'N', 'Y', 'CAMPLISTS_ONLY_WITH_ALT_ADDR3', 'NONE', 'N', 'N', 'CAMPAIGN', '', 'vdcwebform', 'LEAD', 'Y', 'Y', 'Y', 'DISABLED', 'N', '', 'ASK', 'N', '', 'US_thanks_no_contact', 'NI', '8300', '', 'US_thanks_no_contact', 'NI', '8300', '0', 'N', 'N', 'DISABLED', 'NONE', 'MANUAL', 'N', 'N', 'N', 'N', 'NONE', 'N', '', '', 'N', '2016-10-12 13:36:04', 'N', '', 'NONE', '', 1, '', 'VARhttps://dispo.apps-2.gad-inc.com/api/call/leadCalled?vendor_lead_code=--A--vendor_lead_code--B--&dispo=--A--dispo--B--&phone_number=--A--phone_number--B--&user=--A--user--B--&usergroup=--A--user_group--B--&talk_time=--A--talk_time--B--&source_id=--A--source_id--B--&lead_id=--A--lead_id--B--&agent_log_id=--A--agent_log_id--B--&campaign=--A--campaign--B--', '', '', '', 'N', 'NONE', 'DISABLED', 'N', 'ALL_ACTIVE', 'NONE', 'DISABLED', 'Someone is blind monitoring your session', '', 'DISABLED', '', 'DISABLED', 'DISABLED', '', 'ENABLED', 5, 'NONE', 'DISABLED', '', 'PREVIEW_AND_SKIP', 'CALLS_WAIT_CUST_ACW_PAUSE', 'Y', '1', 0, 'Y', 'STANDARD', 'DISABLED', 'Y', 'N', 'LEAD_ASCEND', 'DISABLED', 'UNCHECKED', 'ENABLED', 'CAMPLISTS_ALL', '', 'N', 'PRECAL', 'N', 'CAMPAIGN', 'DISABLED', 'DISABLED', 'DISABLED', 0, 'LOGGED-IN_AGENTS', 3, 'buzz', '', '', 0, 'ADAPT_CALC_ONLY', 'DISPO_ENABLED', '', '--SYSTEM-SETTINGS--', 'CALLID', 'VARhttps://dispo.apps-2.gad-inc.com/api/call/leadCalled?vendor_lead_code=--A--vendor_lead_code--B--&dispo=--A--dispo--B--&phone_number=--A--phone_number--B--&user=--A--user--B--&source_id=--A--source_id--B--&lead_id=--A--lead_id--B--&campaign=--A--campaign--B--', 'N', 'DISABLED', 50, 10, 3, 0, 'DISABLED', 'Supervisors', 'N', 'DISABLED', 'CAMPAIGN_SELECTED', 'DISABLED', 'DISABLED', 'DISABLED', '', 'N', '---NONE---', '---NONE---', 10, 'N', 0, 'DCMX', 0, 'DISMX', 0, 0, 'SELECTED_LOCK', 'N', 0, 'ENABLED', 'DISABLED', 0, 'N', 'N', 'DISABLED', 'DISABLED', 'DISABLED', 'CLICK', 'ENABLED', 'DISABLED', 'DISABLED', 'NONE', '', 'ENABLED', 'ENABLED', 30, '', 'N', '', 'N', 'N', 0, 'N', 0, 'N', 'N', 'DISABLED', 'N', 0, 0, 'N', 'N', 'N', 'DEFAULT', 'DEFAULT', 'DISABLED', 'DISABLED', '', 0, 'GADMAINROTATE', 'PAUSMX', 'N', 0, 'DISABLED', 'NO', '', '', 'N', 'DISABLED', 'DISABLED', 'ENABLED', 'DISABLED', 'N', 'N', 'DISABLED', 'DISABLED', 0, null, 'DISABLED', '', 'DISABLED', '---NONE---', 'DISABLED', 'AMD', 0, '', 'DISABLED', '---NONE---', 50, 'DISABLED', '', 'N', 0, 'DISABLED', 'NONE', 99, '', '', '', 'ACKNOWLEDGE', 'DISABLED', 'DISABLED', 'DISABLED', 'DISABLED', 'DISABLED', 'DISABLED', 0, 'DISABLED', 'DISABLED', 'DISABLED', 'SYSTEM_WIDE', 0, 'DISABLED', '---DISABLED---', 0, 0, '', 'N', 'GROUP_ID_UP', 'DEFAULT', 0, 'DISABLED'),
        ('BASES', 'Base SENSITIVE Campaign', 'N', '', '', '', '', '', 'RANDOM', '', '', 'VARhttp://***********/PHScripts/PHScriptReaderVici.php?vicidial=true&typeid=--A--vendor_lead_code--B--&agent_login=--A--user--B--&confirmation=true', 'Y', 100, '1', 'longest_wait_time', '8am-8pm', '', 50, '997', '7207299878', '8368', '8309', 'ALLFORCE', 'FULLDATE_CUSTPHONE', 'NEWSCRSSL', 'SCRIPT', 'SIP-6-silence', 'N', '', '', '', '', 'N', 'N', 'NONE', 7, 'HANGUP', '8307', 'Y', 0, 'Wrapup Call', null, 'N', 0, 'N', 'RATIO', 'N', '30', '4.0', '1400', '0', 0, 'AUTO', 'NONE', ' B N NA DC -', 'N', '', '2024-05-29 12:15:37', 'N', '2016-10-12 16:02:29', ' NA AL AHK SVYHU PU PDROP NLP N ERI DROP CB AA B UA A UAWR NEW -', 'N', 'Y', 'DISABLED', 'N', 998, '---NONE---', ' CCCLOSER -', 50, '---NONE---', 'Y', ' SA SALE -', '', '24HRMIDNIGHT', 'NONE', 'Y', '', '', 'US_pol_survey_hello', '1238', '8', 'US_pol_survey_transfer', 'US_thanks_no_contact', 'AGENT_XFER', 'OPTIN', 'NI', '1-DEMOCRAT|2-REPUBLICAN|3-INDEPENDANT|8-OPTOUT|X-NO RESPONSE|', '8300', '/home/<USER>', 'N', 'Y', 'CAMPLISTS_ONLY', 'NONE', 'N', 'N', 'CAMPAIGN', '', 'vdcwebform', 'LEAD', 'Y', 'Y', 'Y', 'DISABLED', 'N', '', 'ASK', 'N', '', 'US_thanks_no_contact', 'NI', '8300', '', 'US_thanks_no_contact', 'NI', '8300', '0', 'N', 'N', 'DISABLED', 'NONE', 'MANUAL', 'N', 'N', 'N', 'N', 'NONE', 'N', '', '', 'N', null, 'N', '', 'NONE', '', 1, '', 'VARhttps://dispo.apps-2.gad-inc.com/api/call/leadCalled?vendor_lead_code=--A--vendor_lead_code--B--&dispo=--A--dispo--B--&phone_number=--A--phone_number--B--&user=--A--user--B--&usergroup=--A--user_group--B--&talk_time=--A--talk_time--B--&source_id=--A--source_id--B--&lead_id=--A--lead_id--B--&agent_log_id=--A--agent_log_id--B--&campaign=--A--campaign--B--', '', '', '', 'N', 'NONE', 'DISABLED', 'N', 'ALL_ACTIVE', 'NONE', 'DISABLED', 'Someone is blind monitoring your session', '', 'DISABLED', '', 'DISABLED', 'DISABLED', '', 'ENABLED', 5, 'NONE', 'DISABLED', '', 'PREVIEW_AND_SKIP', 'CALLS_WAIT_CUST_ACW_PAUSE', 'Y', '1', 0, 'Y', 'STANDARD', 'DISABLED', 'Y', 'N', 'LEAD_ASCEND', 'DISABLED', 'UNCHECKED', 'ENABLED', 'CAMPLISTS_ALL', '', 'N', 'PRECAL', 'N', 'CAMPAIGN', 'DISABLED', 'DISABLED', 'DISABLED', 0, 'LOGGED-IN_AGENTS', 3, 'buzz', '', '', 0, 'ADAPT_CALC_ONLY', 'DISPO_ENABLED', '', '--SYSTEM-SETTINGS--', 'CALLID', 'VARhttps://dispo.apps-2.gad-inc.com/api/call/leadCalled?vendor_lead_code=--A--vendor_lead_code--B--&dispo=--A--dispo--B--&phone_number=--A--phone_number--B--&user=--A--user--B--&source_id=--A--source_id--B--&lead_id=--A--lead_id--B--&campaign=--A--campaign--B--', 'N', 'DISABLED', 50, 10, 3, 0, 'DISABLED', 'Supervisors', 'N', 'DISABLED', 'CAMPAIGN_SELECTED', 'DISABLED', 'DISABLED', 'DISABLED', '', 'N', '---NONE---', '---NONE---', 10, 'N', 0, 'DCMX', 0, 'DISMX', 0, 0, 'SELECTED_LOCK', 'N', 0, 'ENABLED', 'DISABLED', 0, 'N', 'N', 'DISABLED', 'DISABLED', 'DISABLED', 'CLICK', 'ENABLED', 'DISABLED', 'DISABLED', 'NONE', '', 'ENABLED', 'ENABLED', 30, '', 'N', '', 'N', 'N', 0, 'N', 0, 'N', 'N', 'DISABLED', 'N', 0, 0, 'N', 'N', 'N', 'DEFAULT', 'DEFAULT', 'DISABLED', 'DISABLED', '', 0, 'GADMAINROTATE', 'PAUSMX', 'N', 0, 'DISABLED', 'NO', '', '', 'N', 'DISABLED', 'DISABLED', 'ENABLED', 'DISABLED', 'N', 'N', 'DISABLED', 'DISABLED', 0, null, 'DISABLED', '', 'DISABLED', '---NONE---', 'DISABLED', 'AMD', 0, '', 'DISABLED', '---NONE---', 50, 'DISABLED', '', 'N', 0, 'DISABLED', 'NONE', 99, '', '', '', 'ACKNOWLEDGE', 'DISABLED', 'DISABLED', 'DISABLED', 'DISABLED', 'DISABLED', 'DISABLED', 0, 'DISABLED', 'DISABLED', 'DISABLED', 'SYSTEM_WIDE', 0, 'DISABLED', '---DISABLED---', 0, 0, '', 'N', 'GROUP_ID_UP', 'DEFAULT', 0, 'DISABLED');