DROP TABLE IF EXISTS `vicidial_campaigns`;
CREATE TABLE `vicidial_campaigns` (
  `campaign_id` varchar(8) COLLATE utf8_unicode_ci NOT NULL,
  `campaign_name` varchar(40) COLLATE utf8_unicode_ci DEFAULT NULL,
  `active` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT NULL,
  `dial_status_a` varchar(6) COLLATE utf8_unicode_ci DEFAULT NULL,
  `dial_status_b` varchar(6) COLLATE utf8_unicode_ci DEFAULT NULL,
  `dial_status_c` varchar(6) COLLATE utf8_unicode_ci DEFAULT NULL,
  `dial_status_d` varchar(6) COLLATE utf8_unicode_ci DEFAULT NULL,
  `dial_status_e` varchar(6) COLLATE utf8_unicode_ci DEFAULT NULL,
  `lead_order` varchar(30) COLLATE utf8_unicode_ci DEFAULT NULL,
  `park_ext` varchar(10) COLLATE utf8_unicode_ci DEFAULT NULL,
  `park_file_name` varchar(100) COLLATE utf8_unicode_ci DEFAULT 'default',
  `web_form_address` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `allow_closers` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT NULL,
  `hopper_level` int(8) unsigned DEFAULT 1,
  `auto_dial_level` varchar(6) COLLATE utf8_unicode_ci DEFAULT '0',
  `next_agent_call` varchar(40) COLLATE utf8_unicode_ci DEFAULT 'longest_wait_time',
  `local_call_time` varchar(10) COLLATE utf8_unicode_ci DEFAULT '9am-9pm',
  `voicemail_ext` varchar(10) COLLATE utf8_unicode_ci DEFAULT NULL,
  `dial_timeout` tinyint(3) unsigned DEFAULT 60,
  `dial_prefix` varchar(20) COLLATE utf8_unicode_ci DEFAULT '9',
  `campaign_cid` varchar(20) COLLATE utf8_unicode_ci DEFAULT '0000000000',
  `campaign_vdad_exten` varchar(20) COLLATE utf8_unicode_ci DEFAULT '8368',
  `campaign_rec_exten` varchar(20) COLLATE utf8_unicode_ci DEFAULT '8309',
  `campaign_recording` enum('NEVER','ONDEMAND','ALLCALLS','ALLFORCE') COLLATE utf8_unicode_ci DEFAULT 'ONDEMAND',
  `campaign_rec_filename` varchar(50) COLLATE utf8_unicode_ci DEFAULT 'FULLDATE_CUSTPHONE',
  `campaign_script` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
  `get_call_launch` enum('NONE','SCRIPT','SCRIPTTWO','WEBFORM','WEBFORMTWO','WEBFORMTHREE','FORM','PREVIEW_WEBFORM','PREVIEW_WEBFORMTWO','PREVIEW_WEBFORMTHREE','PREVIEW_SCRIPT','PREVIEW_SCRIPTTWO','PREVIEW_FORM') COLLATE utf8_unicode_ci DEFAULT 'NONE',
  `am_message_exten` varchar(100) COLLATE utf8_unicode_ci DEFAULT 'vm-goodbye',
  `amd_send_to_vmx` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `xferconf_a_dtmf` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `xferconf_a_number` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `xferconf_b_dtmf` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `xferconf_b_number` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `alt_number_dialing` enum('N','Y','SELECTED','SELECTED_TIMER_ALT','SELECTED_TIMER_ADDR3','UNSELECTED','UNSELECTED_TIMER_ALT','UNSELECTED_TIMER_ADDR3') COLLATE utf8_unicode_ci DEFAULT 'N',
  `scheduled_callbacks` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `lead_filter_id` varchar(20) COLLATE utf8_unicode_ci DEFAULT 'NONE',
  `drop_call_seconds` tinyint(3) DEFAULT 5,
  `drop_action` enum('HANGUP','MESSAGE','VOICEMAIL','IN_GROUP','AUDIO','CALLMENU','VMAIL_NO_INST') COLLATE utf8_unicode_ci DEFAULT 'AUDIO',
  `safe_harbor_exten` varchar(20) COLLATE utf8_unicode_ci DEFAULT '8307',
  `display_dialable_count` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'Y',
  `wrapup_seconds` smallint(3) unsigned DEFAULT 0,
  `wrapup_message` varchar(255) COLLATE utf8_unicode_ci DEFAULT 'Wrapup Call',
  `closer_campaigns` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `use_internal_dnc` enum('Y','N','AREACODE') COLLATE utf8_unicode_ci DEFAULT 'N',
  `allcalls_delay` smallint(3) unsigned DEFAULT 0,
  `omit_phone_code` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `dial_method` enum('MANUAL','RATIO','ADAPT_HARD_LIMIT','ADAPT_TAPERED','ADAPT_AVERAGE','INBOUND_MAN','SHARED_RATIO','SHARED_ADAPT_HARD_LIMIT','SHARED_ADAPT_TAPERED','SHARED_ADAPT_AVERAGE') COLLATE utf8_unicode_ci DEFAULT 'MANUAL',
  `available_only_ratio_tally` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `adaptive_dropped_percentage` varchar(4) COLLATE utf8_unicode_ci DEFAULT '3',
  `adaptive_maximum_level` varchar(6) COLLATE utf8_unicode_ci DEFAULT '3.0',
  `adaptive_latest_server_time` varchar(4) COLLATE utf8_unicode_ci DEFAULT '2100',
  `adaptive_intensity` varchar(6) COLLATE utf8_unicode_ci DEFAULT '0',
  `adaptive_dl_diff_target` smallint(3) DEFAULT 0,
  `concurrent_transfers` enum('AUTO','1','2','3','4','5','6','7','8','9','10','15','20','25','30','40','50','60','80','100','1000','10000') COLLATE utf8_unicode_ci DEFAULT 'AUTO',
  `auto_alt_dial` enum('NONE','ALT_ONLY','ADDR3_ONLY','ALT_AND_ADDR3','ALT_AND_EXTENDED','ALT_AND_ADDR3_AND_EXTENDED','EXTENDED_ONLY','MULTI_LEAD') COLLATE utf8_unicode_ci DEFAULT 'NONE',
  `auto_alt_dial_statuses` varchar(255) COLLATE utf8_unicode_ci DEFAULT ' B N NA DC -',
  `agent_pause_codes_active` enum('Y','N','FORCE') COLLATE utf8_unicode_ci DEFAULT 'N',
  `campaign_description` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `campaign_changedate` datetime DEFAULT NULL,
  `campaign_stats_refresh` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `campaign_logindate` datetime DEFAULT NULL,
  `dial_statuses` varchar(255) COLLATE utf8_unicode_ci DEFAULT ' NEW -',
  `disable_alter_custdata` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `no_hopper_leads_logins` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `list_order_mix` varchar(20) COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `campaign_allow_inbound` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `manual_dial_list_id` bigint(14) unsigned DEFAULT 998,
  `default_xfer_group` varchar(20) COLLATE utf8_unicode_ci DEFAULT '---NONE---',
  `xfer_groups` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `queue_priority` tinyint(2) DEFAULT 50,
  `drop_inbound_group` varchar(20) COLLATE utf8_unicode_ci DEFAULT '---NONE---',
  `qc_enabled` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `qc_statuses` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `qc_lists` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `qc_shift_id` varchar(20) COLLATE utf8_unicode_ci DEFAULT '24HRMIDNIGHT',
  `qc_get_record_launch` enum('NONE','SCRIPT','WEBFORM','QCSCRIPT','QCWEBFORM') COLLATE utf8_unicode_ci DEFAULT 'NONE',
  `qc_show_recording` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'Y',
  `qc_web_form_address` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `qc_script` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
  `survey_first_audio_file` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `survey_dtmf_digits` varchar(16) COLLATE utf8_unicode_ci DEFAULT '1238',
  `survey_ni_digit` varchar(1) COLLATE utf8_unicode_ci DEFAULT '8',
  `survey_opt_in_audio_file` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `survey_ni_audio_file` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `survey_method` enum('AGENT_XFER','VOICEMAIL','EXTENSION','HANGUP','CAMPREC_60_WAV','CALLMENU','VMAIL_NO_INST') COLLATE utf8_unicode_ci DEFAULT 'AGENT_XFER',
  `survey_no_response_action` enum('OPTIN','OPTOUT','DROP') COLLATE utf8_unicode_ci DEFAULT 'OPTIN',
  `survey_ni_status` varchar(6) COLLATE utf8_unicode_ci DEFAULT 'NI',
  `survey_response_digit_map` varchar(255) COLLATE utf8_unicode_ci DEFAULT '1-DEMOCRAT|2-REPUBLICAN|3-INDEPENDANT|8-OPTOUT|X-NO RESPONSE|',
  `survey_xfer_exten` varchar(20) COLLATE utf8_unicode_ci DEFAULT '8300',
  `survey_camp_record_dir` varchar(255) COLLATE utf8_unicode_ci DEFAULT '/home/<USER>',
  `disable_alter_custphone` enum('Y','N','HIDE') COLLATE utf8_unicode_ci DEFAULT 'Y',
  `display_queue_count` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'Y',
  `manual_dial_filter` varchar(50) COLLATE utf8_unicode_ci DEFAULT 'NONE',
  `agent_clipboard_copy` varchar(50) COLLATE utf8_unicode_ci DEFAULT 'NONE',
  `agent_extended_alt_dial` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `use_campaign_dnc` enum('Y','N','AREACODE') COLLATE utf8_unicode_ci DEFAULT 'N',
  `three_way_call_cid` enum('CAMPAIGN','CUSTOMER','AGENT_PHONE','AGENT_CHOOSE','CUSTOM_CID') COLLATE utf8_unicode_ci DEFAULT 'CAMPAIGN',
  `three_way_dial_prefix` varchar(20) COLLATE utf8_unicode_ci DEFAULT '',
  `web_form_target` varchar(100) COLLATE utf8_unicode_ci NOT NULL DEFAULT 'vdcwebform',
  `vtiger_search_category` varchar(100) COLLATE utf8_unicode_ci DEFAULT 'LEAD',
  `vtiger_create_call_record` enum('Y','N','DISPO') COLLATE utf8_unicode_ci DEFAULT 'Y',
  `vtiger_create_lead_record` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'Y',
  `vtiger_screen_login` enum('Y','N','NEW_WINDOW') COLLATE utf8_unicode_ci DEFAULT 'Y',
  `cpd_amd_action` enum('DISABLED','DISPO','MESSAGE','CALLMENU','INGROUP') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `agent_allow_group_alias` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `default_group_alias` varchar(30) COLLATE utf8_unicode_ci DEFAULT '',
  `vtiger_search_dead` enum('DISABLED','ASK','RESURRECT') COLLATE utf8_unicode_ci DEFAULT 'ASK',
  `vtiger_status_call` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `survey_third_digit` varchar(1) COLLATE utf8_unicode_ci DEFAULT '',
  `survey_third_audio_file` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `survey_third_status` varchar(6) COLLATE utf8_unicode_ci DEFAULT 'NI',
  `survey_third_exten` varchar(20) COLLATE utf8_unicode_ci DEFAULT '8300',
  `survey_fourth_digit` varchar(1) COLLATE utf8_unicode_ci DEFAULT '',
  `survey_fourth_audio_file` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `survey_fourth_status` varchar(6) COLLATE utf8_unicode_ci DEFAULT 'NI',
  `survey_fourth_exten` varchar(20) COLLATE utf8_unicode_ci DEFAULT '8300',
  `drop_lockout_time` varchar(6) COLLATE utf8_unicode_ci DEFAULT '0',
  `quick_transfer_button` varchar(20) COLLATE utf8_unicode_ci DEFAULT 'N',
  `prepopulate_transfer_preset` enum('N','PRESET_1','PRESET_2','PRESET_3','PRESET_4','PRESET_5') COLLATE utf8_unicode_ci DEFAULT 'N',
  `drop_rate_group` varchar(20) COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `view_calls_in_queue` enum('NONE','ALL','1','2','3','4','5') COLLATE utf8_unicode_ci DEFAULT 'NONE',
  `view_calls_in_queue_launch` enum('AUTO','MANUAL') COLLATE utf8_unicode_ci DEFAULT 'MANUAL',
  `grab_calls_in_queue` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `call_requeue_button` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `pause_after_each_call` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `no_hopper_dialing` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `agent_dial_owner_only` enum('NONE','USER','TERRITORY','USER_GROUP','USER_BLANK','TERRITORY_BLANK','USER_GROUP_BLANK') COLLATE utf8_unicode_ci DEFAULT 'NONE',
  `agent_display_dialable_leads` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `web_form_address_two` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `waitforsilence_options` varchar(25) COLLATE utf8_unicode_ci DEFAULT '',
  `agent_select_territories` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `campaign_calldate` datetime DEFAULT NULL,
  `crm_popup_login` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `crm_login_address` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `timer_action` varchar(20) COLLATE utf8_unicode_ci DEFAULT 'NONE',
  `timer_action_message` varchar(255) COLLATE utf8_unicode_ci DEFAULT '',
  `timer_action_seconds` mediumint(7) DEFAULT -1,
  `start_call_url` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `dispo_call_url` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `xferconf_c_number` varchar(50) COLLATE utf8_unicode_ci DEFAULT '',
  `xferconf_d_number` varchar(50) COLLATE utf8_unicode_ci DEFAULT '',
  `xferconf_e_number` varchar(50) COLLATE utf8_unicode_ci DEFAULT '',
  `use_custom_cid` enum('Y','N','AREACODE','USER_CUSTOM_1','USER_CUSTOM_2','USER_CUSTOM_3','USER_CUSTOM_4','USER_CUSTOM_5') COLLATE utf8_unicode_ci DEFAULT 'N',
  `scheduled_callbacks_alert` enum('NONE','BLINK','RED','BLINK_RED','BLINK_DEFER','RED_DEFER','BLINK_RED_DEFER') COLLATE utf8_unicode_ci DEFAULT 'NONE',
  `queuemetrics_callstatus_override` enum('DISABLED','NO','YES') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `extension_appended_cidname` enum('Y','N','Y_USER','Y_WITH_CAMPAIGN','Y_USER_WITH_CAMPAIGN') COLLATE utf8_unicode_ci DEFAULT 'N',
  `scheduled_callbacks_count` enum('LIVE','ALL_ACTIVE') COLLATE utf8_unicode_ci DEFAULT 'ALL_ACTIVE',
  `manual_dial_override` enum('NONE','ALLOW_ALL','DISABLE_ALL') COLLATE utf8_unicode_ci DEFAULT 'NONE',
  `blind_monitor_warning` enum('DISABLED','ALERT','NOTICE','AUDIO','ALERT_NOTICE','ALERT_AUDIO','NOTICE_AUDIO','ALL') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `blind_monitor_message` varchar(255) COLLATE utf8_unicode_ci DEFAULT 'Someone is blind monitoring your session',
  `blind_monitor_filename` varchar(100) COLLATE utf8_unicode_ci DEFAULT '',
  `inbound_queue_no_dial` enum('DISABLED','ENABLED','ALL_SERVERS','ENABLED_WITH_CHAT','ALL_SERVERS_WITH_CHAT') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `timer_action_destination` varchar(30) COLLATE utf8_unicode_ci DEFAULT '',
  `enable_xfer_presets` enum('DISABLED','ENABLED','STAGING','CONTACTS') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `hide_xfer_number_to_dial` enum('DISABLED','ENABLED') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `manual_dial_prefix` varchar(20) COLLATE utf8_unicode_ci DEFAULT '',
  `customer_3way_hangup_logging` enum('DISABLED','ENABLED') COLLATE utf8_unicode_ci DEFAULT 'ENABLED',
  `customer_3way_hangup_seconds` smallint(5) unsigned DEFAULT 5,
  `customer_3way_hangup_action` enum('NONE','DISPO') COLLATE utf8_unicode_ci DEFAULT 'NONE',
  `ivr_park_call` enum('DISABLED','ENABLED','ENABLED_PARK_ONLY','ENABLED_BUTTON_HIDDEN') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `ivr_park_call_agi` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `manual_preview_dial` enum('DISABLED','PREVIEW_AND_SKIP','PREVIEW_ONLY') COLLATE utf8_unicode_ci DEFAULT 'PREVIEW_AND_SKIP',
  `realtime_agent_time_stats` enum('DISABLED','WAIT_CUST_ACW','WAIT_CUST_ACW_PAUSE','CALLS_WAIT_CUST_ACW_PAUSE') COLLATE utf8_unicode_ci DEFAULT 'CALLS_WAIT_CUST_ACW_PAUSE',
  `use_auto_hopper` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'Y',
  `auto_hopper_multi` varchar(6) COLLATE utf8_unicode_ci DEFAULT '1',
  `auto_hopper_level` mediumint(8) unsigned DEFAULT 0,
  `auto_trim_hopper` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'Y',
  `api_manual_dial` enum('STANDARD','QUEUE','QUEUE_AND_AUTOCALL') COLLATE utf8_unicode_ci DEFAULT 'STANDARD',
  `manual_dial_call_time_check` enum('DISABLED','ENABLED') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `display_leads_count` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `lead_order_randomize` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `lead_order_secondary` enum('LEAD_ASCEND','LEAD_DESCEND','CALLTIME_ASCEND','CALLTIME_DESCEND','VENDOR_ASCEND','VENDOR_DESCEND') COLLATE utf8_unicode_ci DEFAULT 'LEAD_ASCEND',
  `per_call_notes` enum('ENABLED','DISABLED') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `my_callback_option` enum('CHECKED','UNCHECKED') COLLATE utf8_unicode_ci DEFAULT 'UNCHECKED',
  `agent_lead_search` enum('ENABLED','LIVE_CALL_INBOUND','LIVE_CALL_INBOUND_AND_MANUAL','DISABLED') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `agent_lead_search_method` varchar(30) COLLATE utf8_unicode_ci DEFAULT 'CAMPLISTS_ALL',
  `queuemetrics_phone_environment` varchar(20) COLLATE utf8_unicode_ci DEFAULT '',
  `auto_pause_precall` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `auto_pause_precall_code` varchar(6) COLLATE utf8_unicode_ci DEFAULT 'PRECAL',
  `auto_resume_precall` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `manual_dial_cid` enum('CAMPAIGN','AGENT_PHONE','AGENT_PHONE_OVERRIDE') COLLATE utf8_unicode_ci DEFAULT 'CAMPAIGN',
  `post_phone_time_diff_alert` varchar(30) COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `custom_3way_button_transfer` varchar(30) COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `available_only_tally_threshold` enum('DISABLED','LOGGED-IN_AGENTS','NON-PAUSED_AGENTS','WAITING_AGENTS') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `available_only_tally_threshold_agents` smallint(5) unsigned DEFAULT 0,
  `dial_level_threshold` enum('DISABLED','LOGGED-IN_AGENTS','NON-PAUSED_AGENTS','WAITING_AGENTS') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `dial_level_threshold_agents` smallint(5) unsigned DEFAULT 0,
  `safe_harbor_audio` varchar(100) COLLATE utf8_unicode_ci DEFAULT 'buzz',
  `safe_harbor_menu_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT '',
  `survey_menu_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT '',
  `callback_days_limit` smallint(3) DEFAULT 0,
  `dl_diff_target_method` enum('ADAPT_CALC_ONLY','CALLS_PLACED') COLLATE utf8_unicode_ci DEFAULT 'ADAPT_CALC_ONLY',
  `disable_dispo_screen` enum('DISPO_ENABLED','DISPO_DISABLED','DISPO_SELECT_DISABLED') COLLATE utf8_unicode_ci DEFAULT 'DISPO_ENABLED',
  `disable_dispo_status` varchar(6) COLLATE utf8_unicode_ci DEFAULT '',
  `screen_labels` varchar(20) COLLATE utf8_unicode_ci DEFAULT '--SYSTEM-SETTINGS--',
  `status_display_fields` varchar(30) COLLATE utf8_unicode_ci DEFAULT 'CALLID',
  `na_call_url` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `survey_recording` enum('Y','N','Y_WITH_AMD') COLLATE utf8_unicode_ci DEFAULT 'N',
  `pllb_grouping` enum('DISABLED','ONE_SERVER_ONLY','CASCADING') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `pllb_grouping_limit` smallint(5) DEFAULT 50,
  `call_count_limit` smallint(5) unsigned DEFAULT 0,
  `call_count_target` smallint(5) unsigned DEFAULT 3,
  `callback_hours_block` tinyint(2) DEFAULT 0,
  `callback_list_calltime` enum('ENABLED','DISABLED') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `user_group` varchar(20) COLLATE utf8_unicode_ci DEFAULT '---ALL---',
  `hopper_vlc_dup_check` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `in_group_dial` enum('DISABLED','MANUAL_DIAL','NO_DIAL','BOTH') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `in_group_dial_select` enum('AGENT_SELECTED','CAMPAIGN_SELECTED','ALL_USER_GROUP') COLLATE utf8_unicode_ci DEFAULT 'CAMPAIGN_SELECTED',
  `safe_harbor_audio_field` varchar(30) COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `pause_after_next_call` enum('ENABLED','DISABLED') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `owner_populate` enum('ENABLED','DISABLED') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `use_other_campaign_dnc` varchar(8) COLLATE utf8_unicode_ci DEFAULT '',
  `allow_emails` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `amd_inbound_group` varchar(20) COLLATE utf8_unicode_ci DEFAULT '',
  `amd_callmenu` varchar(50) COLLATE utf8_unicode_ci DEFAULT '',
  `survey_wait_sec` tinyint(3) DEFAULT 10,
  `manual_dial_lead_id` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `dead_max` smallint(5) unsigned DEFAULT 0,
  `dead_max_dispo` varchar(6) COLLATE utf8_unicode_ci DEFAULT 'DCMX',
  `dispo_max` smallint(5) unsigned DEFAULT 0,
  `dispo_max_dispo` varchar(6) COLLATE utf8_unicode_ci DEFAULT 'DISMX',
  `pause_max` smallint(5) unsigned DEFAULT 0,
  `max_inbound_calls` smallint(5) unsigned DEFAULT 0,
  `manual_dial_search_checkbox` enum('SELECTED','SELECTED_RESET','UNSELECTED','UNSELECTED_RESET','SELECTED_LOCK','UNSELECTED_LOCK') COLLATE utf8_unicode_ci DEFAULT 'SELECTED',
  `hide_call_log_info` enum('Y','N','SHOW_1','SHOW_2','SHOW_3','SHOW_4','SHOW_5','SHOW_6','SHOW_7','SHOW_8','SHOW_9','SHOW_10') COLLATE utf8_unicode_ci DEFAULT 'N',
  `timer_alt_seconds` smallint(5) DEFAULT 0,
  `wrapup_bypass` enum('DISABLED','ENABLED') COLLATE utf8_unicode_ci DEFAULT 'ENABLED',
  `wrapup_after_hotkey` enum('DISABLED','ENABLED') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `callback_active_limit` smallint(5) unsigned DEFAULT 0,
  `callback_active_limit_override` enum('N','Y') COLLATE utf8_unicode_ci DEFAULT 'N',
  `allow_chats` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `comments_all_tabs` enum('DISABLED','ENABLED') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `comments_dispo_screen` enum('DISABLED','ENABLED','REPLACE_CALL_NOTES') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `comments_callback_screen` enum('DISABLED','ENABLED','REPLACE_CB_NOTES') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `qc_comment_history` enum('CLICK','AUTO_OPEN','CLICK_ALLOW_MINIMIZE','AUTO_OPEN_ALLOW_MINIMIZE') COLLATE utf8_unicode_ci DEFAULT 'CLICK',
  `show_previous_callback` enum('DISABLED','ENABLED') COLLATE utf8_unicode_ci DEFAULT 'ENABLED',
  `clear_script` enum('DISABLED','ENABLED') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `cpd_unknown_action` enum('DISABLED','DISPO','MESSAGE','CALLMENU','INGROUP') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `manual_dial_search_filter` varchar(50) COLLATE utf8_unicode_ci DEFAULT 'NONE',
  `web_form_address_three` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `manual_dial_override_field` enum('ENABLED','DISABLED') COLLATE utf8_unicode_ci DEFAULT 'ENABLED',
  `status_display_ingroup` enum('ENABLED','DISABLED') COLLATE utf8_unicode_ci DEFAULT 'ENABLED',
  `customer_gone_seconds` smallint(5) unsigned DEFAULT 30,
  `agent_display_fields` varchar(100) COLLATE utf8_unicode_ci DEFAULT '',
  `am_message_wildcards` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `manual_dial_timeout` varchar(3) COLLATE utf8_unicode_ci DEFAULT '',
  `routing_initiated_recordings` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `manual_dial_hopper_check` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `callback_useronly_move_minutes` mediumint(5) unsigned DEFAULT 0,
  `ofcom_uk_drop_calc` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `manual_auto_next` smallint(5) unsigned DEFAULT 0,
  `manual_auto_show` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `allow_required_fields` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `dead_to_dispo` enum('ENABLED','DISABLED') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `agent_xfer_validation` enum('N','Y') COLLATE utf8_unicode_ci DEFAULT 'N',
  `ready_max_logout` mediumint(7) DEFAULT 0,
  `callback_display_days` smallint(3) DEFAULT 0,
  `three_way_record_stop` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `hangup_xfer_record_start` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `scheduled_callbacks_email_alert` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `max_inbound_calls_outcome` enum('DEFAULT','ALLOW_AGENTDIRECT','ALLOW_MI_PAUSE','ALLOW_AGENTDIRECT_AND_MI_PAUSE') COLLATE utf8_unicode_ci DEFAULT 'DEFAULT',
  `manual_auto_next_options` enum('DEFAULT','PAUSE_NO_COUNT') COLLATE utf8_unicode_ci DEFAULT 'DEFAULT',
  `agent_screen_time_display` varchar(40) COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `next_dial_my_callbacks` enum('DISABLED','ENABLED') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `inbound_no_agents_no_dial_container` varchar(40) COLLATE utf8_unicode_ci DEFAULT '---DISABLED---',
  `inbound_no_agents_no_dial_threshold` smallint(5) DEFAULT 0,
  `cid_group_id` varchar(20) COLLATE utf8_unicode_ci DEFAULT '---DISABLED---',
  `pause_max_dispo` varchar(6) COLLATE utf8_unicode_ci DEFAULT 'PAUSMX',
  `script_top_dispo` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `dead_trigger_seconds` smallint(5) DEFAULT 0,
  `dead_trigger_action` enum('DISABLED','AUDIO','URL','AUDIO_AND_URL') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `dead_trigger_repeat` enum('NO','REPEAT_ALL','REPEAT_AUDIO','REPEAT_URL') COLLATE utf8_unicode_ci DEFAULT 'NO',
  `dead_trigger_filename` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `dead_trigger_url` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `scheduled_callbacks_force_dial` enum('N','Y') COLLATE utf8_unicode_ci DEFAULT 'N',
  `scheduled_callbacks_auto_reschedule` varchar(10) COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `scheduled_callbacks_timezones_container` varchar(40) COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `three_way_volume_buttons` varchar(20) COLLATE utf8_unicode_ci DEFAULT 'ENABLED',
  `callback_dnc` enum('ENABLED','DISABLED') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `manual_dial_validation` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `mute_recordings` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `auto_active_list_new` varchar(20) COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `call_quota_lead_ranking` varchar(40) COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `call_quota_process_running` tinyint(3) DEFAULT 0,
  `call_quota_last_run_date` datetime DEFAULT NULL,
  `sip_event_logging` varchar(40) COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `campaign_script_two` varchar(20) COLLATE utf8_unicode_ci DEFAULT '',
  `leave_vm_no_dispo` enum('ENABLED','DISABLED') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `leave_vm_message_group_id` varchar(40) COLLATE utf8_unicode_ci DEFAULT '---NONE---',
  `dial_timeout_lead_container` varchar(40) COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `amd_type` enum('AMD','CPD','KHOMP') COLLATE utf8_unicode_ci DEFAULT 'AMD',
  `vmm_daily_limit` tinyint(3) unsigned DEFAULT 0,
  `opensips_cid_name` varchar(15) COLLATE utf8_unicode_ci DEFAULT '',
  `amd_agent_route_options` enum('ENABLED','DISABLED','PENDING') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `browser_alert_sound` varchar(20) COLLATE utf8_unicode_ci DEFAULT '---NONE---',
  `browser_alert_volume` tinyint(3) unsigned DEFAULT 50,
  `three_way_record_stop_exception` varchar(40) COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `pause_max_exceptions` varchar(40) COLLATE utf8_unicode_ci DEFAULT '',
  `hopper_drop_run_trigger` varchar(1) COLLATE utf8_unicode_ci DEFAULT 'N',
  `daily_call_count_limit` tinyint(3) unsigned DEFAULT 0,
  `daily_limit_manual` varchar(20) COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `transfer_button_launch` varchar(12) COLLATE utf8_unicode_ci DEFAULT 'NONE',
  `shared_dial_rank` tinyint(3) DEFAULT 99,
  `agent_search_method` varchar(2) COLLATE utf8_unicode_ci DEFAULT '',
  `qc_scorecard_id` varchar(20) COLLATE utf8_unicode_ci DEFAULT '',
  `qc_statuses_id` varchar(20) COLLATE utf8_unicode_ci DEFAULT '',
  `clear_form` enum('DISABLED','ENABLED','ACKNOWLEDGE') COLLATE utf8_unicode_ci DEFAULT 'ACKNOWLEDGE',
  `leave_3way_start_recording` enum('DISABLED','ALL_CALLS','ALL_BUT_EXCEPTIONS','ONLY_EXCEPTIONS') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `leave_3way_start_recording_exception` varchar(40) COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `calls_waiting_vl_one` varchar(25) COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `calls_waiting_vl_two` varchar(25) COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `calls_inqueue_count_one` varchar(40) COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `calls_inqueue_count_two` varchar(40) COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `in_man_dial_next_ready_seconds` smallint(5) unsigned DEFAULT 0,
  `in_man_dial_next_ready_seconds_override` varchar(40) COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `transfer_no_dispo` enum('DISABLED','EXTERNAL_ONLY','LOCAL_ONLY','LEAVE3WAY_ONLY','LOCAL_AND_EXTERNAL','LOCAL_AND_LEAVE3WAY','LEAVE3WAY_AND_EXTERNAL','LOCAL_AND_EXTERNAL_AND_LEAVE3WAY') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `call_limit_24hour_method` enum('DISABLED','PHONE_NUMBER','LEAD') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `call_limit_24hour_scope` enum('SYSTEM_WIDE','CAMPAIGN_LISTS') COLLATE utf8_unicode_ci DEFAULT 'SYSTEM_WIDE',
  `call_limit_24hour` tinyint(3) unsigned DEFAULT 0,
  `call_limit_24hour_override` varchar(40) COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  `cid_group_id_two` varchar(20) COLLATE utf8_unicode_ci DEFAULT '---DISABLED---',
  `incall_tally_threshold_seconds` smallint(5) unsigned DEFAULT 0,
  `auto_alt_threshold` tinyint(3) unsigned DEFAULT 0,
  `pause_max_url` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `agent_hide_hangup` enum('Y','N') COLLATE utf8_unicode_ci DEFAULT 'N',
  `ig_xfer_list_sort` enum('GROUP_ID_UP','GROUP_ID_DOWN','GROUP_NAME_UP','GROUP_NAME_DOWN','PRIORITY_UP','PRIORITY_DOWN') COLLATE utf8_unicode_ci DEFAULT 'GROUP_ID_UP',
  `script_tab_frame_size` varchar(10) COLLATE utf8_unicode_ci DEFAULT 'DEFAULT',
  `max_logged_in_agents` smallint(5) unsigned DEFAULT 0,
  `user_group_script` enum('DISABLED','ENABLED') COLLATE utf8_unicode_ci DEFAULT 'DISABLED',
  PRIMARY KEY (`campaign_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci