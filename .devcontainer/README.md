# Dev Container Use

A development container is a container with a pre-installed set of tools and pre-configured environment.

In our case, we use an Ubuntu 24.04 LTE base container with ASDF installed for Rust,Elixir/Erlang. We may later add NodeJS, but we may alternitively make a separate dev-container for the script-builder-node-react project.

This requires Dev Containers https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-containers

## What's in the environment?

The machine comes pre-installed with the following tools:

- MySQL Client, MariaDB & MySQL compat & Dump/Restore tools
- Redis Client & Dump/Restore tools
- Postgres Client & Dump/Restore tools
- MongoDB (MongoSH shell) & Dump/Restore tools
- git, curl, vim, nano, etc.
- zsh, preconfigured with persistant history

Along with the container itself, Visual Studio Code will launch a number of services:

- CRM Local DB (Core Data, Loader is WIP)
- Wireless Local DB (Core Data, No loader)
- Landline Local DB (A clone of Wireless, Landline specific is WIP)
- Admin Local DB
- Redis Local Server (Session Store)
- RabbitMQ Local Cluster
- MongoDB Local Server

Also included are shortcuts to run common commands, such as `ierun` to launch the app but still listen on a port for remote connection.

## Optional Services

If you are needing to experiment with some additional services, feel free to modify your docker-compose.yaml locally.

Example: Add Grafana, and SuperSet
```yaml
services:
  grafana:
    image: grafana/grafana-oss:latest
    ports:
      - "3000:3000"
  superset:
    image: apache/superset:latest
    depends_on:
      - postgres
      - redis
    environment:
      # Basic Superset config
      - SUPERSET_LOAD_EXAMPLES=no
      - SUPERSET_SECRET_KEY=super_secret_key
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=superset_db
    volumes:
      - superset-data:/var/lib/superset
    ports:
      - "8088:8088"
```

## Before starting your container

This hosts a number of services:

- Port 4000, 4369 :: Admin Web, Erlang Cluster Communications (LiveBook Remote)
- Port 3307 :: wireless db - MariaDB
- Port 3308 :: landline db - MariaDB
- Port 3309 :: crm db - MySQL
- Port 5432 :: admin db - PostgreSQL
- Port 6379 :: redis
- Port 27017 :: mongodb
- Port 5762, 15762 :: rabbitmq, web for rabbitmq

These ports will be in use on your local machine, so you must stop local services and docker containers that will conflict.

In the Umbrella App, this can be done by using the `docker-compose stop` in the root of the project. This will stop redis, mongo, etc. You may need to stop your local postgres server if you have one running.

## Starting your container

Open the Umbrella project in Visual Studio Code of your choice (This should work with Cursor).

You may be presented with a pop up that a dev-conainer exists, if so, click "reopen in container". If you don't see this message, hit `CTRL+SHIFT+P` and type "reopen in container".

This will start the build process. Building the container will take many minutes (15-20+), but after the packages and languages are installed and cached, subsequent starts will be nearly instant.

Also during this build process, the Dev Containers extension will download and start all the servives required (see ./devcontainer/docker-compose.yaml).

If there is a failure, hit the "edit locally" button, it will re-open the project and a new tab with the error.

## Accessing Data

See the port mapping above for DB to port mappings.

All local dev DBs are considered insecure and containing unsafe/non-pci data. As such, you can connect with username `root` and empty password for all DBs over their ports.

In DataGrip, go to File > Manage IDE Settings > Import Settings > Browse to the ./.devcontainer/settings.zip file, and click OK, then OK to import them.

Postgres (any):
Username: `postgres`
Password: `postgres`

MySQL (any):
Username: `root`
Password: blank

Redis:
Unauthenticated

Mongo:
Unauthenticated

RabbitMQ
Username: `guest`
Password: `guest`

## Rebuilding, Correcting an issue

This is a repeatable environment, even if the DBs fail or data becomes corrupt, it can be rebuilt quickly.

For example, if you accidentally droped the project table in the CRM, and need to reinitialize the local CRM DB, you would perform the following:

1. `CTRL+SHIFT+P` > "Reopen folder locally" (Don't use the containers while they are being rebuilt)
1. Browse to .devcontainers in a terminal
1. Run `docker-compose --project-name umbrella_devcontainer down -v`
1. `CTRL+SHIFT+P` > "Reopen in container"

This will cause the DB volumes to be rebuilt, but the container images (system files) stay around.

If you want to reset one specific server, rather than all volumes, you can use `docker volume ls` and identify which volume to remove.

## Running the app

Once open, a ZSH shell should be available to you, init the admin DB (`mix ecto.setup.admin`) and run the app with `ierun`.

### Getting Admin Access

Log into the local app once, then change your level from Agent to Admin in the admin_dev.user table.

### Troubleshooting

#### ElixirLS isn't working

Check Output Panel > change the dropdown to "ElixirLS - workspace", check for errors.

Possibly delete `./.elixir_ls` and `./.elixir-tools` and recompile. It's possible that the debug symbols compiled on the host OS is incompatible with the guest dev OS.

#### Compile Times

Your machine is likely Windows or MacOS, but the development container is linux. The combined build dir is `_build`, but this causes issues when swapping back and forth between devcontainers and local development.

To aide this, the `_build_devcontainer` dir is used and overlayed ontop of the `_build` dir, this results in a seamless relocation without changing project files. It is not required for local development.

#### Conflicting Ports / "Port In Use"

Some ports may be in use, the most common ones are:

1. Postgres server running locally
    Stop the local instances, you may restart them for local development.
2. Other containers running, such as `./docker-compose.yaml`
    The devcontainers is a successor to the local docker-compose, stop it with `docker-compose stop` from the root project directory. Restart it for local development.
3. :4369 - epmd is still running from a previous local run or another app.
    - **MacOS:** `lsof -t -n -f -P -i:4369 | xargs kill -9`. This will look for processes using 4369 and kill them. Do not wildly close DB ports, rather identify which are up and close them via shutting down the server properly, as to avoid local data-loss.
    - **Linux (Ubuntu 24.04):** `sudo netstat -tulnp | grep ":4369"`, look at the PID value in the output. `sudo kill -9 <PID>` to kill the process and recheck.
    - **Windows (10 / 11):** Open Command Prompt as Administrator and run `netstat -ano | findstr :4369`. Note the PID in the last column. Open Task Manager, go to the "Details" tab, find the process with the corresponding PID, right-click it, and select "End Task". Alternatively, use `taskkill /PID <PID> /F` to kill the process directly from Command Prompt.
