#!/bin/bash

set -e  # Exit on any error

# Check if languages are already installed
if [ -d "/home/<USER>/.asdf/installs" ]; then
    echo "Languages already installed, skipping installation..."
    exit 0
fi

echo "Installing development languages and tools..."

# Set up environment
export PATH="/home/<USER>/.asdf/bin:/home/<USER>/.asdf/shims:$PATH"
export KERL_BUILD_DOCS=yes

# Install Erlang
echo "Installing Erlang..."
asdf plugin add erlang || true  # Don't fail if plugin already exists
asdf install erlang

# Install Elixir
echo "Installing Elixir..."
asdf plugin add elixir || true
asdf install elixir

# Install Rebar
echo "Installing Rebar..."
asdf plugin add rebar || true
asdf install rebar

# Install Node.js
echo "Installing Node.js..."
asdf plugin add nodejs || true
asdf install nodejs

# Install Rust
echo "Installing Rust..."
asdf plugin add rust || true
asdf install rust

# Refresh shims
asdf reshim

# Install Hex and Rebar for Elixir
echo "Installing Hex and Rebar for Elixir..."
/home/<USER>/.asdf/shims/mix local.hex --force
/home/<USER>/.asdf/shims/mix local.rebar --force

# Rust setup
echo "Setting up Rust..."
rustup default stable

echo "Language installation completed successfully!"