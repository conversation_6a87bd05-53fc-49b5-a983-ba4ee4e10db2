{
    "name": "Elixir Development Environment",
    "features": {
        "ghcr.io/duduribeiro/devcontainer-features/neovim:1": {
            "version": "nightly"
        }
    },
    "customizations": {
        "vscode": {
            "extensions": [
                "jakebecker.elixir-ls",
                "phoenixframework.phoenix",
                "ritvyk.heex-html",
                "bradlc.vscode-tailwindcss",
                "stivo.tailwind-fold",
                "oderwat.indent-rainbow",
                "jamilabreu.vscode-phoenix-snippets",
                "phoenix-liveview-snippets.phoenix-liveview-snippets",
                "ms-vsliveshare.vsliveshare",
                "Augment.vscode-augment",
                "MS-SarifVSCode.sarif-viewer",
                "gp-pereira.refatorex",
                "naumovs.color-highlight"
            ],
            "settings": {
                "terminal.integrated.defaultProfile.linux": "zsh",
                "tailwind-fold.supportedLanguages": [
                    "html",
                    "typescriptreact",
                    "javascriptreact",
                    "typescript",
                    "javascript",
                    "vue-html",
                    "vue",
                    "php",
                    "markdown",
                    "coffeescript",
                    "svelte",
                    "astro",
                    "erb",
                    "phoenix-heex",
                    "html-heex"
                ],
                "indentRainbow.ignoreErrorLanguages": [
                    "html-heex",
                    "phoenix-heex",
                    "elixir",
                    "markdown"
                ]
            }
        }
    },
    "postCreateCommand": "/usr/bin/install-languages.sh && mix do deps.get, deps.compile",
    "forwardPorts": [
        // App, epmd, epmd-com-port, postgres, mysql, redis, mongo
        "dev:4000",
        "dev:4369",
        "livebook:8080",
        "livebook:8081",
        "wireless:3307",
        "landline:3308",
        "crm:3309",
        "postgres:5432",
        "redis:6379",
        "mongo:27017",
        "rabbitmq:5762",
        "rabbitmq:15672"
    ],
    "remoteUser": "ubuntu",
    "mounts": [
        "source=${localWorkspaceFolder},target=/workspace,type=bind,consistency=cached",
        "source=${localWorkspaceFolder}/.devcontainer/.zsh_history,target=/home/<USER>/.zsh_history,type=bind,consistency=cached",
        "source=${localWorkspaceFolder}/_build_devcontainer,target=/workspace/_build,type=bind,consistency=cached",
        "source=${HOME}/.config/nvim,target=/home/<USER>/.config/nvim,consistency=cached"
    ],
    "service": "dev",
    "runServices": [
        "livebook",
        "postgres",
        "wireless",
        "landline",
        "crm",
        "redis",
        "rabbitmq",
        "mongo"
    ],
    "dockerComposeFile": "docker-compose.yaml",
    "workspaceFolder": "/workspace"
}