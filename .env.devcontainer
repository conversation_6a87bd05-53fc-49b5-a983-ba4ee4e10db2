# env independent
# Disable Dev creds.
VITELITY_ENDPOINT="https://api.vitelity.net/api.php"
# VITELITY_USERNAME="gadi_dev"
# VITELITY_PASSWORD="BRE1JTl9WSUNJX"
# VITELITY_SMS_AUTH="65a09373bb2dc"
VITELITY_USERNAME=""
VITELITY_PASSWORD=""
VITELITY_SMS_AUTH=""

OMEDA_API_AUTH="78BF32E3-FE3F-4756-A619-3E382854B123"

DNC_SCRUB_SFTP_ENDPOINT="securetransfer.dncscrub.com"
DNC_SCRUB_USERNAME="GRVN"
DNC_SCRUB_PASSWORD="rlfvDo4sca"
DNC_SCRUB_API_AUTH="0308A7808EBF50C4592EBCF54241DB20768E571D1462"

RND_SFTP_ENDPOINT="sftp.reassigned.us"
RND_USERNAME="rnd-prod-sftp-C275359519"
RND_KEY_PATH="/workspace/.ssh/id_rnd"
# RND_API_AUTH="" # Did we dodge API? 🔫

OPENAI_API_KEY="********************************************************************************************************************************************************************"

ADMIN_REDIS_URI="redis://redis"
ADMIN_SHOPIFY_URL="https://the-chosen-merch.myshopify.com/admin/api/graphql.json"
ADMIN_SHOPIFY_ACCESS_TOKEN="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"
ADMIN_GORGIAS_URL="https://thechosengifts.gorgias.com/api/"
ADMIN_GORGIAS_AUTHORIZATION_TOKEN="Basic ********************************************************************************************************************************"
ADMIN_GORGIAS_SHOPIFY_INTEGRATION_ID="34482"

ADMIN_VICI_SALT="xACWyQktwbJGpfVE"
ADMIN_CROSS_TALK_KEY="ASiUFZYA904Nmg6KgmKKV9GuQFTDlBtMtFPBgs5URhSr8uIJDM1aoTGpf/zJTL/6NEot1IVsmGA2r8KS62ykcg=="
ADMIN_CROSS_TALK_ENDPOINT_URL="https://tools.gad-inc.com/cross_talk"
ADMIN_SENTRY_DSN="http://b21561aff25f4d12bfb4cac230cf93bb@10.20.206.19:9000/1"

ADMIN_AWS_ACCESS_KEY_ID="********************"
ADMIN_AWS_SECRET_ACCESS_KEY="zEKdfUDJzAsbjI/mYdEE3z0xMoIwKLNeG2Dx0eSx"

# Post Processing
ADMIN_POST_PROCESSING=false
ADMIN_POST_PROCESSING_WORKERS=0

# AMQP
#AMQP_URL=""
AMQP_URL="amqp://guest:guest@rabbitmq:5672"
#AMQP_URL="amqp://cat-1-webhooks:xEsnoc-9duqga-xactuc@*************:5672"
AMQP_CONSUMER_ENABLE=true
#AMQP_CONSUMER_ENABLE=false

# MongoDB
MONGODB_URL="mongodb://mongo:27017"
# MONGODB_URL="amqp://cat-2-tool-suite:hiffuh-dyNcyb-9havke@*************"


# Dev MS config
ADMIN_MICROSOFT_CLIENT_ID="df6760ac-ff3f-486b-a066-de45615ac5d6"
ADMIN_MICROSOFT_CLIENT_SECRET="****************************************" #"****************************************"
ADMIN_MICROSOFT_TENANT_ID="46c320c9-f586-478f-82f2-18bf890269a9"
ADMIN_MICROSOFT_SCOPE="https://graph.microsoft.com/User.Read openid profile offline_access"
ADMIN_MICROSOFT_REDIRECT_URI="http://localhost:4000/oauth/callbacks/microsoft"

# Prod MS config
# ADMIN_MICROSOFT_CLIENT_ID="df6760ac-ff3f-486b-a066-de45615ac5d6"
# ADMIN_MICROSOFT_CLIENT_SECRET="****************************************"
# ADMIN_MICROSOFT_TENANT_ID="46c320c9-f586-478f-82f2-18bf890269a9"
# ADMIN_MICROSOFT_REDIRECT_URI="https://admin.gad-inc.com/oauth/callbacks/microsoft"
# ADMIN_MICROSOFT_SCOPE="https://graph.microsoft.com/User.Read openid profile offline_access"

# env dependent
## POINT CRM TO PROD
CRM_REPO_USERNAME="root"
CRM_REPO_PASSWORD=""
CRM_REPO_HOSTNAME="crm"
CRM_REPO_DATABASE="crm"
CRM_REPO_PORT=3309

CRM_REPO_REPLICA1_USERNAME="root"
CRM_REPO_REPLICA1_PASSWORD=""
CRM_REPO_REPLICA1_HOSTNAME="crm"
CRM_REPO_REPLICA1_DATABASE="crm"
CRM_REPO_REPLICA1_PORT=3309

## POINT CRM TO LOCALHOST
# CRM_REPO_USERNAME="root"
# CRM_REPO_PASSWORD=""
# CRM_REPO_HOSTNAME="localhost"
# CRM_REPO_DATABASE="crm"
#
# CRM_REPO_REPLICA1_USERNAME="root"
# CRM_REPO_REPLICA1_PASSWORD=""
# CRM_REPO_REPLICA1_HOSTNAME="localhost"
# CRM_REPO_REPLICA1_DATABASE="crm"

ADMIN_REPO_USERNAME="postgres"
ADMIN_REPO_PASSWORD="postgres"
ADMIN_REPO_HOSTNAME="postgres"
ADMIN_REPO_DATABASE="admin_dev"

ADMIN_PROD_REPO_USERNAME="postgres"
ADMIN_PROD_REPO_PASSWORD=""
ADMIN_PROD_REPO_HOSTNAME="postgres"
ADMIN_PROD_REPO_DATABASE="admin_prod"

ADMIN_TEST_REPO_USERNAME="postgres"
ADMIN_TEST_REPO_PASSWORD="postgres"
ADMIN_TEST_REPO_HOSTNAME="postgres"
ADMIN_TEST_REPO_DATABASE="admin_test"

DIALER_LANDLINE_REPO_REPLICA1_USERNAME="mysql_user"
DIALER_LANDLINE_REPO_REPLICA1_PASSWORD="mysql_pass"
DIALER_LANDLINE_REPO_REPLICA1_HOSTNAME="landline"
DIALER_LANDLINE_REPO_REPLICA1_DATABASE="asterisk"
DIALER_LANDLINE_REPO_REPLICA1_PORT=3308

DIALER_LANDLINE_REPO_USERNAME="mysql_user"
DIALER_LANDLINE_REPO_PASSWORD="mysql_pass"
DIALER_LANDLINE_REPO_HOSTNAME="landline"
DIALER_LANDLINE_REPO_DATABASE="asterisk"
DIALER_LANDLINE_REPO_PORT=3308

DIALER_WIRELESS_REPO_REPLICA1_USERNAME="mysql_user"
DIALER_WIRELESS_REPO_REPLICA1_PASSWORD="mysql_pass"
DIALER_WIRELESS_REPO_REPLICA1_HOSTNAME="wireless"
DIALER_WIRELESS_REPO_REPLICA1_DATABASE="asterisk"
DIALER_WIRELESS_REPO_REPLICA1_PORT=3307

DIALER_WIRELESS_REPO_USERNAME="mysql_user"
DIALER_WIRELESS_REPO_PASSWORD="mysql_pass"
DIALER_WIRELESS_REPO_HOSTNAME="wireless"
DIALER_WIRELESS_REPO_DATABASE="asterisk"
DIALER_WIRELESS_REPO_PORT=3307
