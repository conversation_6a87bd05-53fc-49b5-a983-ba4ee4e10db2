defmodule Admin.AdminRepo.Migrations.ChangeRndFields do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:contact_staging) do
      modify :expire_date, :date
      modify :qual_date, :date
    end
  end

  def down do
    alter table(:contact_staging) do
      modify :qual_date, :naive_datetime
      modify :expire_date, :naive_datetime
    end
  end
end
