defmodule Admin.AdminRepo.Migrations.AddRndFields do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:contact_staging) do
      add :qual_date, :naive_datetime
      add :expire_date, :naive_datetime
      add :term, :bigint
    end

    alter table(:setups) do
      add :do_rnd?, :boolean, default: false
    end
  end

  def down do
    alter table(:setups) do
      remove :do_rnd?
    end

    alter table(:contact_staging) do
      remove :term
      remove :expire_date
      remove :qual_date
    end
  end
end
