{
	"LiveView def apply_action/3": {
		"scope": "elixir",
		"prefix": "def apply_action",
		"description": "Define an action for the live_action",
		"body": [
			// This sucks. I can't get it to properly show the ending `}` in the param field without a slash.
			"defp apply_action(socket, ${1::index}, ${2|%{\"id\" => id \\},_params|}) do",
    		"	socket",
    		"	|> assign(:page_title, \"View ${3:${1:/capitalize}}\")",
    		"	$0",
  			"end"
		]
	}
	// Place your workspace workspace snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and 
	// description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope 
	// is left empty or omitted, the snippet gets applied to all languages. The prefix is what is 
	// used to trigger the snippet and the body will be expanded and inserted. Possible variables are: 
	// $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders. 
	// Placeholders with the same ids are connected.
	// Example:
	// "Print to console": {
	// 	"scope": "javascript,typescript",
	// 	"prefix": "log",
	// 	"body": [
	// 		"console.log('$1');",
	// 		"$2"
	// 	],
	// 	"description": "Log output to console"
	// }
}