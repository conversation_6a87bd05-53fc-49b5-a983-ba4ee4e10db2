{
	"LiveView defmodule": {
		"scope": "elixir",
		"prefix": "defmodule",
		"description": "Outlines a Phoenix module.",
		"body": [
			"defmodule ${1:${TM_FILEPATH/.*\\/lib\\/(.+)\\.ex/$1/g}} do",
			"	@moduledoc \"\"\"",
			"	$5",
			"	\"\"\"",
			"	${2:using ${3:AdminWeb}, ${4|:live_view,:live_component,:component|}",
			"",
			"	$0",
			"end"
		]
	},
	"Phoenix LiveComponent def update/2": {
		"scope": "elixir",
		"prefix": "def update",
		"description": "LiveComponent callback...",
		"body": [
			"@impl Phoenix.LiveComponent",
			"def update(${1:%{${2:id: id}\\} = }assigns, socket) do",
			"${3:	socket = }",
			"	$0",
			"	{:ok, socket}",
			"end"
		]
	}
	// Place your workspace workspace snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and 
	// description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope 
	// is left empty or omitted, the snippet gets applied to all languages. The prefix is what is 
	// used to trigger the snippet and the body will be expanded and inserted. Possible variables are: 
	// $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders. 
	// Placeholders with the same ids are connected.
	// Example:
	// "Print to console": {
	// 	"scope": "javascript,typescript",
	// 	"prefix": "log",
	// 	"body": [
	// 		"console.log('$1');",
	// 		"$2"
	// 	],
	// 	"description": "Log output to console"
	// }
}