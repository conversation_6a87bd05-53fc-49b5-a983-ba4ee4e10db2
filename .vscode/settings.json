{"rust-analyzer.linkedProjects": ["native/fileloader/Cargo.toml", "native/bcrypt/Cargo.toml"], "elixirLS.autoBuild": false, "elixirLS.autoInsertRequiredAlias": false, "elixirLS.dialyzerEnabled": true, "elixirLS.mixEnv": "dev", "elixirLS.stdlibSrcDir": "/workspace/.local_backups/elixir/", "docker.enableDockerComposeLanguageService": false, "elixirLS.trace.server": "messages", "emmet.includeLanguages": {"elixir": "html", "phoenix-heex": "html"}, "tailwindCSS.includeLanguages": {"phoenix-heex": "html", "elixir": "html"}, "tailwindCSS.emmetCompletions": true, "elixirLS.suggestSpecs": false, "files.associations": {".heex": "phoenix-hex", ".css": "tailwind-css"}, "[elixir]": {"editor.wordWrap": "off", "editor.formatOnSaveMode": "modificationsIfAvailable", "editor.formatOnType": false, "diffEditor.codeLens": true, "editor.minimap.renderCharacters": false, "editor.minimap.sectionHeaderFontSize": 8, "editor.minimap.showSlider": "always", "editor.inlineSuggest.suppressSuggestions": true, "editor.inlineSuggest.syntaxHighlightingEnabled": true, "editor.quickSuggestions": {"comments": "on", "strings": "on"}, "editor.quickSuggestionsDelay": 5, "editor.suggest.insertMode": "replace", "editor.suggest.preview": true, "editor.suggest.snippetsPreventQuickSuggestions": true, "files.autoSaveWhenNoErrors": true, "files.autoSaveWorkspaceFilesOnly": true, "emmet.triggerExpansionOnTab": true, "tailwindCSS.colorDecorators": false}, "editor.wordWrap": "off", "outline.problems.colors": false, "augment.nextEdit.enableAutoApply": true, "terminal.integrated.shellIntegration.enabled": true, "files.watcherExclude": {"**/_build_devcontainer/**": true, "**/_build/**": true, "**/.elixir_ls/**": true}, "files.exclude": {"_build": true, "_build_devcontainer": true, ".local_backups": true, "cover": true, "data_entry_samples": true, "deps": true, "deps_repos": true, ".elixir_ls": true, "doc/dist": true}, "search.exclude": {".git, doc/dist, cover, credo.sarif, data_entry_samples, deps, _build, _build_container, docs, assets, .elixir_ls": true}}