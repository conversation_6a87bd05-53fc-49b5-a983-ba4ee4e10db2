defmodule AdminWeb.AdminConnCase do
  @moduledoc """
  This module defines the test case to be used by
  tests that require setting up a Phoenix/LiveView connection.

  Such tests rely on `Phoenix.ConnTest` and also
  import other functionality to make it easier
  to build common data structures and query the data layer.

  Finally, we enable the SQL sandbox. Such that changes done to the database
  are reverted at the end of every test.

  Notes: We use PostgreSQL for the Admin/AdminWeb sections of the suite. If
  you are only testing against `Admin.AdminRepo`;you can run database tests
  asynchronously by setting `use AdminWeb.ConnCase, async: true`. Although
  this option is not recommended for other databases such as Either ViciDial
  DBs, the CRM, or any non-SQL backed server.
  """

  use ExUnit.CaseTemplate

  import Ecto.Adapters.SQL.Sandbox,
    only: [checkin: 1, checkout: 1, mode: 2, start_owner!: 2, stop_owner: 1]

  using do
    quote do
      # The default endpoint for testing
      @endpoint AdminWeb.Endpoint

      use AdminWeb, :verified_routes

      alias Admin.AdminRepo, as: Repo

      import Phoenix.ConnTest
      import Phoenix.LiveViewTest
      # Import conveniences for testing with connections
      import Plug.Conn

      import Admin.DataCase

      import AdminWeb.AdminConnCase
      import AdminWeb.TestHelpers

      import Ash.Changeset
      import Ecto.Changeset
      import Ecto.Query

      import Oban.Testing

      import Mox
    end
  end

  setup tags do
    AdminWeb.AdminConnCase.setup_sandbox(tags)
    :ok
  end

  def log_in_user(conn, user) do
    import Plug.Conn

    conn
    |> Phoenix.ConnTest.init_test_session(%{})
    |> assign(:current_user, user)
    |> put_session(:user_name, user.name)
    |> put_session(:user_id, user.id)
    |> put_session(:live_socket_id, "users_sessions:#{user.id}")
  end

  def setup_sandbox(tags) do
    shared = not Map.get(tags, :async, false)
    admin_pid = start_owner!(Admin.AdminRepo, shared: shared)
    crm_pid = start_owner!(Crm.Repo, shared: shared)
    landline_pid = start_owner!(Dialer.Landline.Repo, shared: shared)
    wireless_pid = start_owner!(Dialer.Wireless.Repo, shared: shared)

    on_exit(fn ->
      stop_owner(admin_pid)
      stop_owner(crm_pid)
      stop_owner(landline_pid)
      stop_owner(wireless_pid)
    end)
  end

  def db_cleanup(repo, list) when is_list(list) do
    :ok = Enum.each(list, &db_cleanup(repo, &1))
  end

  def db_cleanup(repo, sql) do
    repo
    |> Ecto.Adapters.SQL.query!(sql)
  end

  @doc """
  Checks out the admin database and shares it with the current process.
  """
  def checkout_and_share_admin_db do
    mode(Admin.AdminRepo, {:shared, self()})
    :ok = checkout(Admin.AdminRepo)

    on_exit(fn ->
      checkin(Admin.AdminRepo)
    end)

    :ok
  end

  @doc """
  Checks out the databases for the test case.
  """
  def checkout_dbs do
    :ok = Ecto.Adapters.SQL.Sandbox.checkout(Admin.AdminRepo)
    :ok = Ecto.Adapters.SQL.Sandbox.checkout(Crm.Repo)
    :ok = Ecto.Adapters.SQL.Sandbox.checkout(Dialer.Landline.Repo)
    :ok = Ecto.Adapters.SQL.Sandbox.checkout(Dialer.Wireless.Repo)
    :ok
  end
end
