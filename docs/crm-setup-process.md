# CRM Setup Process Documentation

This document describes the comprehensive process of performing a CRM setup from the LiveView interface, including the background jobs, data processing, and external integrations.

## Table of Contents

1. [Overview](#overview)
2. [Core Components](#core-components)
3. [LiveView Workflow](#liveview-workflow)
4. [Background Processing](#background-processing)
5. [External Integrations](#external-integrations)
6. [Data Flow Diagram](#data-flow-diagram)

## Overview

The CRM setup process is a multi-stage workflow that transforms raw lead files into processable data for telecommunications campaigns. The process involves file upload, data validation, compliance scrubbing, analysis, and loading into various dialer systems.

### Setup States

A setup progresses through these states:
- **new**: Initial setup creation
- **staged**: Files uploaded and staged
- **scrubbed**: DNC compliance scrubbing complete  
- **analyzed**: Data analysis and metrics complete
- **loading**: Loading data into dialers
- **loaded**: Setup complete and ready for campaigns
- **archived**: Completed and archived
- **discarded**: Setup cancelled/discarded

## Core Components

### Admin.Crm.LeadFile

A LeadFile represents an individual file containing leads to be processed. Key attributes:

- **State Machine**: Tracks file through processing stages (new → staged → scrubbed → analyzed → loaded)
- **File Metadata**: Size, format, modification date, record count
- **Processing Status**: Error states, job IDs, loaded timestamps
- **Relationships**: Belongs to Setup, has many StagedContacts, SourceMetrics, LoadRecords

```elixir
# LeadFile states:
:new -> :staged -> :scrubbed -> :analyzed -> :loading -> :loaded
```

### Admin.Crm.List

The List module provides a context wrapper around LeadFile data processing:

- **DataFrame Integration**: Uses Explorer.DataFrame for efficient data operations
- **Data Validation**: Phone number validation, state code normalization
- **Metrics Collection**: Phone field analysis, data quality metrics
- **Processing Pipeline**: State removal, data cleaning, field mapping

## LiveView Workflow

### 1. Setup Index (`/crm/setups`)

**File**: `lib/admin_web/live/setup_live/index.ex:1`

- Lists all setups with pagination and filtering by state
- Provides setup creation, editing, and management
- Shows setup progress indicators and stage information
- Handles setup archival and discard operations

```mermaid
graph TD
    A[Setup Index] --> B[New Setup]
    A --> C[Edit Setup]
    A --> D[Show Setup Details]
    B --> E[Upload Phase]
    D --> E
    E --> Map[Mapping Phase]
    D --> Map
    Map --> Plan[Planning Phase]
    D --> Plan
    Plan --> F[Process Phase]
    F --> Rev[Review Phase]
    Rev --> Rep[Reporting Phase]
    D --> Rev
    D --> Rep
```

### 2. Upload Phase (`/crm/setups/:id/upload`)

**File**: `lib/admin_web/live/setup_live/upload.ex:1`

**Purpose**: Upload and validate lead files with field mapping

**Key Functions**:
- File upload to S3 via Phoenix.LiveView.Upload
- Automatic field mapping with pattern recognition
- Manual mapping validation and correction
- Sample data preview with warnings

**Process**:
1. User uploads files (CSV, TXT, XLSX, XLS formats)
2. Files uploaded to S3 and LeadFile records created  
3. Field mapping automatically detected using patterns
4. User validates/corrects field mappings
5. Required fields validated (homephone, homestate, sourcecode)

```mermaid
sequenceDiagram
    participant User
    participant UploadLive
    participant S3
    participant Database
    
    User->>UploadLive: Upload files
    UploadLive->>S3: Upload to S3
    S3-->>UploadLive: S3 path
    UploadLive->>Database: Create LeadFile
    UploadLive->>User: Show field mapping
    User->>UploadLive: Validate mappings
    UploadLive->>Database: Save field mappings
```

### 3. Plan Phase (`/crm/setups/:id/plan`)

**File**: `lib/admin_web/live/setup_live/plan.ex:1`

**Purpose**: Configure processing tasks for each lead file

**Key Functions**:
- Create LoadRecord for each LeadFile without tasks
- Configure dialer targets (landline/wireless)
- Set campaign naming and segment configuration
- Validate processing requirements before job spawn

**Task Types**:
- **Load**: Standard loading into dialer systems
- **Append**: Add records to existing campaigns
- **Suppress**: Apply suppression rules

### 4. Process Phase (`/crm/setups/:id/process`)

**File**: `lib/admin_web/live/setup_live/process.ex:1`

**Purpose**: Monitor background job execution and handle user prompts

**Key Functions**:
- Real-time job output display via PubSub
- Group calling decision prompts
- Recovery operations for failed jobs
- DNC scrub results visualization

**Job Spawning Process** (`plan.ex:347`):
```elixir
# Spawns LeadFileProcessor jobs for each lead file
defp spawn_job(%LeadFile{} = lead_file) do
  args = %{
    "setup" => lead_file.setup_id,
    "lead_file" => lead_file.id
  }
  
  # Schedule job 2 seconds in future for UI transition
  very_soon = DateTime.utc_now() |> DateTime.add(2, :second)
  
  id = args
    |> LeadFileProcessor.new(scheduled_at: very_soon)
    |> Admin.Oban.insert!()
    |> then(& &1.id)
end
```

### 5. Review Phase (`/crm/setups/:id/review`)

**Purpose**: Final validation before campaign activation

- Project and campaign summary
- Source breakdown analysis  
- Load summary with record counts
- Final approval for campaign go-live

## Background Processing

### LeadFileProcessor Job

**File**: `lib/admin/crm/lead_file/lead_file_processor.ex:1`

The main background worker that processes individual lead files through the complete pipeline.

**Processing Steps**:

1. **File Download & Validation**
   - Download from S3
   - Parse file format (CSV/TXT/XLSX)
   - Validate file structure

2. **Lead Staging** 
   - Create Admin.Crm.List from file data
   - Apply field mappings from upload phase
   - Stage contacts in `staged_contacts` table
   - Handle partial staging failures by de-staging existing records

3. **DNC Scrubbing**
   - Upload to DNCScrub SFTP server
   - Monitor scrub completion
   - Download scrub results
   - Merge with global DNC state
   - Apply dial policy interpretation

4. **Data Analysis**
   - Generate source metrics breakdown
   - Create phone number reports  
   - Validate data quality
   - Store analysis results

5. **Task Execution**
   - Execute LoadRecord tasks (dialer loading)
   - Execute OperationRecord tasks (append/suppress)
   - Handle group calling decisions
   - Update completion status

```mermaid
graph TD
    A[LeadFileProcessor Job] --> B[Download File from S3]
    B --> C[Create Admin.Crm.List]
    C --> D[Stage Contacts]
    D --> E[DNC Scrub Upload]
    E --> F[Wait for Scrub]
    F --> G[Download Scrub Results]
    G --> H[Analyze Data]
    H --> I[Execute Load Tasks]
    I --> J[Complete Processing]
    
    subgraph "Data Processing"
        D1[Field Mapping]
        D2[State Normalization] 
        D3[Phone Validation]
        D4[Data Cleaning]
        D1 --> D2 --> D3 --> D4
    end
    
    C --> D1
```

### LoadRecordProcessor

**File**: `lib/admin/crm/lead_file/load_record_processor.ex:1`

Handles loading processed leads into dialer systems:

1. **Dialer Loading**
   - Load into Landline/Wireless dialers
   - Handle single vs dual-segment loading
   - Create projects and campaigns
   - Apply state-specific removal rules

2. **SMS Batch Processing** 
   - Create SMS message batches
   - Schedule delivery if configured
   - Handle state removals for SMS compliance

3. **Group Calling Logic**
   - Analyze lead density for group calling recommendations
   - Prompt users via PubSub for group calling decisions
   - Execute group calling setup if approved

## External Integrations

### DNC Scrub (SFTP/API)

**Files**: 
- `lib/admin/integrations/dnc_scrub/dnc_scrub.ex:1`
- `lib/admin/integrations/dnc_scrub/external_sftp.ex:1`

**Purpose**: Compliance scrubbing against Do Not Call registries

**SFTP Process**:
1. Build CSV with phone numbers and DialIDs
2. Zip file using `Admin.Integrations.DNCScrub.ZipHelper`
3. Upload to `/ProjectID/_Upload` directory
4. Call `/app/main/rpc/ftpUploadComplete` to trigger scan
5. Monitor `/ProjectID/_Download` for results
6. Download, validate, and store scrub results in S3
7. Clean up SFTP files

**Result Codes**:
- **B**: Blocked
- **C**: Clean (can call)
- **D**: DNC Match (cannot call)
- **E**: EBR - Currently valid
- **F**: Valid EBR & Wireless (restrictions apply)

### AWS S3 Storage

**File**: `lib/admin/services/s3.ex:1`

**Purpose**: File storage for lead files and scrub results

**Operations**:
- Upload original lead files with UUID naming
- Store DNC scrub result archives
- Generate presigned URLs for secure downloads
- Handle file metadata and content-type setting

**Storage Paths**:
- Lead files: `gad-s3://leads/{uuid}/{filename}`
- Scrub results: `gad-s3://dnc-scrub/{uuid}/{filename}`

### Database Systems

**Primary Databases**:
- **Admin.AdminRepo** (PostgreSQL): Main application data
- **Crm.Repo** (MySQL): Primary CRM database  
- **Crm.Repo.Replica1** (MySQL): Read replica
- **Dialer.Landline.Repo** (MySQL): Landline dialer data
- **Dialer.Wireless.Repo** (MySQL): Wireless dialer data
- **Crm.Script.Repo** (MongoDB): Script storage

## Data Flow Diagram

```mermaid
flowchart TD
    subgraph "User Interface"
        UI1[Setup Index]
        UI2[Upload Files]
        UI3[Plan Tasks]
        UI4[Process Monitor]
        UI5[Review Results]
    end
    
    subgraph "File Processing"
        FP1[S3 Upload]
        FP2[Field Mapping]
        FP3[Data Validation]
        FP4[Lead Staging]
    end
    
    subgraph "Background Jobs"
        BJ1[LeadFileProcessor]
        BJ2[LoadRecordProcessor]
        BJ3[SMS Batch Jobs]
    end
    
    subgraph "External Services"
        EXT1[DNC Scrub SFTP]
        EXT2[AWS S3]
        EXT3[Dialer APIs]
    end
    
    subgraph "Data Storage"
        DB1[(PostgreSQL)]
        DB2[(MySQL CRM)]
        DB3[(MySQL Dialers)]
        DB4[(MongoDB)]
    end
    
    UI1 --> UI2
    UI2 --> FP1
    FP1 --> EXT2
    UI2 --> FP2
    FP2 --> FP3
    FP3 --> UI3
    
    UI3 --> BJ1
    BJ1 --> FP4
    FP4 --> DB1
    BJ1 --> EXT1
    EXT1 --> EXT2
    BJ1 --> BJ2
    
    BJ2 --> EXT3
    BJ2 --> DB2
    BJ2 --> DB3
    BJ2 --> BJ3
    
    UI4 --> BJ1
    UI4 --> BJ2
    BJ1 --> UI5
    
    DB1 -.-> UI1
    DB1 -.-> UI4
    DB2 -.-> UI5
```

## Key Features

### Real-time Updates
- PubSub integration for live job progress
- WebSocket connections for UI updates
- State synchronization across components

### Error Handling & Recovery
- Job retry mechanisms with Oban
- Setup recovery options (delete loads/all)
- Partial failure handling and cleanup

### Compliance & Validation
- DNC scrubbing integration
- State-based calling restrictions
- Data quality validation and warnings

### Multi-tenancy
- User-specific access controls  
- Setup ownership and permissions
- Audit trail with AshPaperTrail

This comprehensive workflow ensures compliant, efficient processing of lead data from upload through campaign deployment, with robust error handling and real-time monitoring capabilities.