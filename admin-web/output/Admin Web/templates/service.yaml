---
# Source: Admin Web/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: admin-web
  namespace: gitlab-managed-apps
  labels:
    app.kubernetes.io/instance: api-umbrella
    app.kubernetes.io/name: admin-web
spec:
  ports:
    - name: http
      protocol: TCP
      port: 4000
      targetPort: http
    - name: epmd
      protocol: TCP
      port: 4369
      targetPort: epmd
  selector:
    app.kubernetes.io/component: admin-web
    app.kubernetes.io/instance: api-umbrella
    app.kubernetes.io/name: admin-web
  type: ClusterIP
  sessionAffinity: None
  ipFamilies:
    - IPv4
  ipFamilyPolicy: SingleStack
  internalTrafficPolicy: Cluster
